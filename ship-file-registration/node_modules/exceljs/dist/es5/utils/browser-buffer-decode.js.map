{"version": 3, "file": "browser-buffer-decode.js", "names": ["textDecoder", "TextDecoder", "bufferToString", "chunk", "decode", "toString", "exports"], "sources": ["../../../lib/utils/browser-buffer-decode.js"], "sourcesContent": ["// eslint-disable-next-line node/no-unsupported-features/node-builtins\nconst textDecoder = typeof TextDecoder === 'undefined' ? null : new TextDecoder('utf-8');\n\nfunction bufferToString(chunk) {\n  if (typeof chunk === 'string') {\n    return chunk;\n  }\n  if (textDecoder) {\n    return textDecoder.decode(chunk);\n  }\n  return chunk.toString();\n}\n\nexports.bufferToString = bufferToString;\n"], "mappings": ";;AAAA;AACA,MAAMA,WAAW,GAAG,OAAOC,WAAW,KAAK,WAAW,GAAG,IAAI,GAAG,IAAIA,WAAW,CAAC,OAAO,CAAC;AAExF,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK;EACd;EACA,IAAIH,WAAW,EAAE;IACf,OAAOA,WAAW,CAACI,MAAM,CAACD,KAAK,CAAC;EAClC;EACA,OAAOA,KAAK,CAACE,QAAQ,CAAC,CAAC;AACzB;AAEAC,OAAO,CAACJ,cAAc,GAAGA,cAAc"}
{"version": 3, "file": "encryptor.js", "names": ["crypto", "require", "Encryptor", "hash", "algorithm", "createHash", "_len", "arguments", "length", "buffers", "Array", "_key", "update", "<PERSON><PERSON><PERSON>", "concat", "digest", "convertPasswordToHash", "password", "hashAlgorithm", "saltValue", "spinCount", "toLowerCase", "hashes", "getHashes", "indexOf", "Error", "passwordBuffer", "from", "key", "i", "iterator", "alloc", "writeUInt32LE", "toString", "randomBytes", "size", "module", "exports"], "sources": ["../../../lib/utils/encryptor.js"], "sourcesContent": ["'use strict';\n\nconst crypto = require('crypto');\n\nconst Encryptor = {\n  /**\n   * Calculate a hash of the concatenated buffers with the given algorithm.\n   * @param {string} algorithm - The hash algorithm.\n   * @returns {Buffer} The hash\n   */\n  hash(algorithm, ...buffers) {\n    const hash = crypto.createHash(algorithm);\n    hash.update(Buffer.concat(buffers));\n    return hash.digest();\n  },\n  /**\n   * Convert a password into an encryption key\n   * @param {string} password - The password\n   * @param {string} hashAlgorithm - The hash algoritm\n   * @param {string} saltValue - The salt value\n   * @param {number} spinCount - The spin count\n   * @param {number} keyBits - The length of the key in bits\n   * @param {<PERSON>uffer} blockKey - The block key\n   * @returns {Buffer} The encryption key\n   */\n  convertPasswordToHash(password, hashAlgorithm, saltValue, spinCount) {\n    hashAlgorithm = hashAlgorithm.toLowerCase();\n    const hashes = crypto.getHashes();\n    if (hashes.indexOf(hashAlgorithm) < 0) {\n      throw new Error(`Hash algorithm '${hashAlgorithm}' not supported!`);\n    }\n\n    // Password must be in unicode buffer\n    const passwordBuffer = Buffer.from(password, 'utf16le');\n    // Generate the initial hash\n    let key = this.hash(hashAlgorithm, Buffer.from(saltValue, 'base64'), passwordBuffer);\n    // Now regenerate until spin count\n    for (let i = 0; i < spinCount; i++) {\n      const iterator = Buffer.alloc(4);\n      // this is the 'special' element of Excel password hashing\n      // that stops us from using crypto.pbkdf2()\n      iterator.writeUInt32LE(i, 0);\n      key = this.hash(hashAlgorithm, key, iterator);\n    }\n    return key.toString('base64');\n  },\n  /**\n   * Generates cryptographically strong pseudo-random data.\n   * @param size The size argument is a number indicating the number of bytes to generate.\n   */\n  randomBytes(size) {\n    return crypto.randomBytes(size);\n  },\n};\nmodule.exports = Encryptor;\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,MAAM,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAEhC,MAAMC,SAAS,GAAG;EAChB;AACF;AACA;AACA;AACA;EACEC,IAAIA,CAACC,SAAS,EAAc;IAC1B,MAAMD,IAAI,GAAGH,MAAM,CAACK,UAAU,CAACD,SAAS,CAAC;IAAC,SAAAE,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADzBC,OAAO,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAPF,OAAO,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;IAAA;IAExBR,IAAI,CAACS,MAAM,CAACC,MAAM,CAACC,MAAM,CAACL,OAAO,CAAC,CAAC;IACnC,OAAON,IAAI,CAACY,MAAM,CAAC,CAAC;EACtB,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,qBAAqBA,CAACC,QAAQ,EAAEC,aAAa,EAAEC,SAAS,EAAEC,SAAS,EAAE;IACnEF,aAAa,GAAGA,aAAa,CAACG,WAAW,CAAC,CAAC;IAC3C,MAAMC,MAAM,GAAGtB,MAAM,CAACuB,SAAS,CAAC,CAAC;IACjC,IAAID,MAAM,CAACE,OAAO,CAACN,aAAa,CAAC,GAAG,CAAC,EAAE;MACrC,MAAM,IAAIO,KAAK,CAAE,mBAAkBP,aAAc,kBAAiB,CAAC;IACrE;;IAEA;IACA,MAAMQ,cAAc,GAAGb,MAAM,CAACc,IAAI,CAACV,QAAQ,EAAE,SAAS,CAAC;IACvD;IACA,IAAIW,GAAG,GAAG,IAAI,CAACzB,IAAI,CAACe,aAAa,EAAEL,MAAM,CAACc,IAAI,CAACR,SAAS,EAAE,QAAQ,CAAC,EAAEO,cAAc,CAAC;IACpF;IACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,SAAS,EAAES,CAAC,EAAE,EAAE;MAClC,MAAMC,QAAQ,GAAGjB,MAAM,CAACkB,KAAK,CAAC,CAAC,CAAC;MAChC;MACA;MACAD,QAAQ,CAACE,aAAa,CAACH,CAAC,EAAE,CAAC,CAAC;MAC5BD,GAAG,GAAG,IAAI,CAACzB,IAAI,CAACe,aAAa,EAAEU,GAAG,EAAEE,QAAQ,CAAC;IAC/C;IACA,OAAOF,GAAG,CAACK,QAAQ,CAAC,QAAQ,CAAC;EAC/B,CAAC;EACD;AACF;AACA;AACA;EACEC,WAAWA,CAACC,IAAI,EAAE;IAChB,OAAOnC,MAAM,CAACkC,WAAW,CAACC,IAAI,CAAC;EACjC;AACF,CAAC;AACDC,MAAM,CAACC,OAAO,GAAGnC,SAAS"}
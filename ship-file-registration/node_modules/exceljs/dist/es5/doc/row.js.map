{"version": 3, "file": "row.js", "names": ["_", "require", "Enums", "co<PERSON><PERSON><PERSON>", "Cell", "Row", "constructor", "worksheet", "number", "_worksheet", "_number", "_cells", "style", "outlineLevel", "commit", "_commitRow", "destroy", "find<PERSON>ell", "colNumber", "getCellEx", "address", "cell", "col", "column", "getColumn", "getCell", "getColumnKey", "l2n", "encodeAddress", "row", "splice", "start", "count", "nKeep", "_len", "arguments", "length", "inserts", "Array", "_key", "nExpand", "nEnd", "i", "cSrc", "cDst", "value", "_comment", "undefined", "eachCell", "options", "iteratee", "includeEmpty", "n", "for<PERSON>ach", "index", "type", "ValueType", "<PERSON><PERSON>", "addPageBreak", "lft", "rght", "ws", "left", "Math", "max", "right", "pb", "id", "man", "min", "rowBreaks", "push", "values", "offset", "hasOwnProperty", "item", "eachColumnKey", "key", "<PERSON><PERSON><PERSON><PERSON>", "some", "cellCount", "actualCellCount", "dimensions", "_applyStyle", "name", "numFmt", "font", "alignment", "protection", "border", "fill", "hidden", "_hidden", "_outlineLevel", "collapsed", "properties", "outlineLevelRow", "model", "cells", "cellModel", "height", "Error", "<PERSON><PERSON><PERSON><PERSON>", "Types", "<PERSON><PERSON>", "decode<PERSON>ddress", "$col$row", "n2l", "JSON", "parse", "stringify", "module", "exports"], "sources": ["../../../lib/doc/row.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('../utils/under-dash');\n\nconst Enums = require('./enums');\nconst colCache = require('../utils/col-cache');\nconst Cell = require('./cell');\n\nclass Row {\n  constructor(worksheet, number) {\n    this._worksheet = worksheet;\n    this._number = number;\n    this._cells = [];\n    this.style = {};\n    this.outlineLevel = 0;\n  }\n\n  // return the row number\n  get number() {\n    return this._number;\n  }\n\n  get worksheet() {\n    return this._worksheet;\n  }\n\n  // Inform Streaming Writer that this row (and all rows before it) are complete\n  // and ready to write. Has no effect on Worksheet document\n  commit() {\n    this._worksheet._commitRow(this); // eslint-disable-line no-underscore-dangle\n  }\n\n  // helps GC by breaking cyclic references\n  destroy() {\n    delete this._worksheet;\n    delete this._cells;\n    delete this.style;\n  }\n\n  findCell(colNumber) {\n    return this._cells[colNumber - 1];\n  }\n\n  // given {address, row, col}, find or create new cell\n  getCellEx(address) {\n    let cell = this._cells[address.col - 1];\n    if (!cell) {\n      const column = this._worksheet.getColumn(address.col);\n      cell = new Cell(this, column, address.address);\n      this._cells[address.col - 1] = cell;\n    }\n    return cell;\n  }\n\n  // get cell by key, letter or column number\n  getCell(col) {\n    if (typeof col === 'string') {\n      // is it a key?\n      const column = this._worksheet.getColumnKey(col);\n      if (column) {\n        col = column.number;\n      } else {\n        col = colCache.l2n(col);\n      }\n    }\n    return (\n      this._cells[col - 1] ||\n      this.getCellEx({\n        address: colCache.encodeAddress(this._number, col),\n        row: this._number,\n        col,\n      })\n    );\n  }\n\n  // remove cell(s) and shift all higher cells down by count\n  splice(start, count, ...inserts) {\n    const nKeep = start + count;\n    const nExpand = inserts.length - count;\n    const nEnd = this._cells.length;\n    let i;\n    let cSrc;\n    let cDst;\n\n    if (nExpand < 0) {\n      // remove cells\n      for (i = start + inserts.length; i <= nEnd; i++) {\n        cDst = this._cells[i - 1];\n        cSrc = this._cells[i - nExpand - 1];\n        if (cSrc) {\n          cDst = this.getCell(i);\n          cDst.value = cSrc.value;\n          cDst.style = cSrc.style;\n          // eslint-disable-next-line no-underscore-dangle\n          cDst._comment = cSrc._comment;\n        } else if (cDst) {\n          cDst.value = null;\n          cDst.style = {};\n          // eslint-disable-next-line no-underscore-dangle\n          cDst._comment = undefined;\n        }\n      }\n    } else if (nExpand > 0) {\n      // insert new cells\n      for (i = nEnd; i >= nKeep; i--) {\n        cSrc = this._cells[i - 1];\n        if (cSrc) {\n          cDst = this.getCell(i + nExpand);\n          cDst.value = cSrc.value;\n          cDst.style = cSrc.style;\n          // eslint-disable-next-line no-underscore-dangle\n          cDst._comment = cSrc._comment;\n        } else {\n          this._cells[i + nExpand - 1] = undefined;\n        }\n      }\n    }\n\n    // now add the new values\n    for (i = 0; i < inserts.length; i++) {\n      cDst = this.getCell(start + i);\n      cDst.value = inserts[i];\n      cDst.style = {};\n      // eslint-disable-next-line no-underscore-dangle\n      cDst._comment = undefined;\n    }\n  }\n\n  // Iterate over all non-null cells in this row\n  eachCell(options, iteratee) {\n    if (!iteratee) {\n      iteratee = options;\n      options = null;\n    }\n    if (options && options.includeEmpty) {\n      const n = this._cells.length;\n      for (let i = 1; i <= n; i++) {\n        iteratee(this.getCell(i), i);\n      }\n    } else {\n      this._cells.forEach((cell, index) => {\n        if (cell && cell.type !== Enums.ValueType.Null) {\n          iteratee(cell, index + 1);\n        }\n      });\n    }\n  }\n\n  // ===========================================================================\n  // Page Breaks\n  addPageBreak(lft, rght) {\n    const ws = this._worksheet;\n    const left = Math.max(0, lft - 1) || 0;\n    const right = Math.max(0, rght - 1) || 16838;\n    const pb = {\n      id: this._number,\n      max: right,\n      man: 1,\n    };\n    if (left) pb.min = left;\n\n    ws.rowBreaks.push(pb);\n  }\n\n  // return a sparse array of cell values\n  get values() {\n    const values = [];\n    this._cells.forEach(cell => {\n      if (cell && cell.type !== Enums.ValueType.Null) {\n        values[cell.col] = cell.value;\n      }\n    });\n    return values;\n  }\n\n  // set the values by contiguous or sparse array, or by key'd object literal\n  set values(value) {\n    // this operation is not additive - any prior cells are removed\n    this._cells = [];\n    if (!value) {\n      // empty row\n    } else if (value instanceof Array) {\n      let offset = 0;\n      if (value.hasOwnProperty('0')) {\n        // contiguous array - start at column 1\n        offset = 1;\n      }\n      value.forEach((item, index) => {\n        if (item !== undefined) {\n          this.getCellEx({\n            address: colCache.encodeAddress(this._number, index + offset),\n            row: this._number,\n            col: index + offset,\n          }).value = item;\n        }\n      });\n    } else {\n      // assume object with column keys\n      this._worksheet.eachColumnKey((column, key) => {\n        if (value[key] !== undefined) {\n          this.getCellEx({\n            address: colCache.encodeAddress(this._number, column.number),\n            row: this._number,\n            col: column.number,\n          }).value = value[key];\n        }\n      });\n    }\n  }\n\n  // returns true if the row includes at least one cell with a value\n  get hasValues() {\n    return _.some(this._cells, cell => cell && cell.type !== Enums.ValueType.Null);\n  }\n\n  get cellCount() {\n    return this._cells.length;\n  }\n\n  get actualCellCount() {\n    let count = 0;\n    this.eachCell(() => {\n      count++;\n    });\n    return count;\n  }\n\n  // get the min and max column number for the non-null cells in this row or null\n  get dimensions() {\n    let min = 0;\n    let max = 0;\n    this._cells.forEach(cell => {\n      if (cell && cell.type !== Enums.ValueType.Null) {\n        if (!min || min > cell.col) {\n          min = cell.col;\n        }\n        if (max < cell.col) {\n          max = cell.col;\n        }\n      }\n    });\n    return min > 0\n      ? {\n          min,\n          max,\n        }\n      : null;\n  }\n\n  // =========================================================================\n  // styles\n  _applyStyle(name, value) {\n    this.style[name] = value;\n    this._cells.forEach(cell => {\n      if (cell) {\n        cell[name] = value;\n      }\n    });\n    return value;\n  }\n\n  get numFmt() {\n    return this.style.numFmt;\n  }\n\n  set numFmt(value) {\n    this._applyStyle('numFmt', value);\n  }\n\n  get font() {\n    return this.style.font;\n  }\n\n  set font(value) {\n    this._applyStyle('font', value);\n  }\n\n  get alignment() {\n    return this.style.alignment;\n  }\n\n  set alignment(value) {\n    this._applyStyle('alignment', value);\n  }\n\n  get protection() {\n    return this.style.protection;\n  }\n\n  set protection(value) {\n    this._applyStyle('protection', value);\n  }\n\n  get border() {\n    return this.style.border;\n  }\n\n  set border(value) {\n    this._applyStyle('border', value);\n  }\n\n  get fill() {\n    return this.style.fill;\n  }\n\n  set fill(value) {\n    this._applyStyle('fill', value);\n  }\n\n  get hidden() {\n    return !!this._hidden;\n  }\n\n  set hidden(value) {\n    this._hidden = value;\n  }\n\n  get outlineLevel() {\n    return this._outlineLevel || 0;\n  }\n\n  set outlineLevel(value) {\n    this._outlineLevel = value;\n  }\n\n  get collapsed() {\n    return !!(\n      this._outlineLevel && this._outlineLevel >= this._worksheet.properties.outlineLevelRow\n    );\n  }\n\n  // =========================================================================\n  get model() {\n    const cells = [];\n    let min = 0;\n    let max = 0;\n    this._cells.forEach(cell => {\n      if (cell) {\n        const cellModel = cell.model;\n        if (cellModel) {\n          if (!min || min > cell.col) {\n            min = cell.col;\n          }\n          if (max < cell.col) {\n            max = cell.col;\n          }\n          cells.push(cellModel);\n        }\n      }\n    });\n\n    return this.height || cells.length\n      ? {\n          cells,\n          number: this.number,\n          min,\n          max,\n          height: this.height,\n          style: this.style,\n          hidden: this.hidden,\n          outlineLevel: this.outlineLevel,\n          collapsed: this.collapsed,\n        }\n      : null;\n  }\n\n  set model(value) {\n    if (value.number !== this._number) {\n      throw new Error('Invalid row number in model');\n    }\n    this._cells = [];\n    let previousAddress;\n    value.cells.forEach(cellModel => {\n      switch (cellModel.type) {\n        case Cell.Types.Merge:\n          // special case - don't add this types\n          break;\n        default: {\n          let address;\n          if (cellModel.address) {\n            address = colCache.decodeAddress(cellModel.address);\n          } else if (previousAddress) {\n            // This is a <c> element without an r attribute\n            // Assume that it's the cell for the next column\n            const {row} = previousAddress;\n            const col = previousAddress.col + 1;\n            address = {\n              row,\n              col,\n              address: colCache.encodeAddress(row, col),\n              $col$row: `$${colCache.n2l(col)}$${row}`,\n            };\n          }\n          previousAddress = address;\n          const cell = this.getCellEx(address);\n          cell.model = cellModel;\n          break;\n        }\n      }\n    });\n\n    if (value.height) {\n      this.height = value.height;\n    } else {\n      delete this.height;\n    }\n\n    this.hidden = value.hidden;\n    this.outlineLevel = value.outlineLevel || 0;\n\n    this.style = (value.style && JSON.parse(JSON.stringify(value.style))) || {};\n  }\n}\n\nmodule.exports = Row;\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,CAAC,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAExC,MAAMC,KAAK,GAAGD,OAAO,CAAC,SAAS,CAAC;AAChC,MAAME,QAAQ,GAAGF,OAAO,CAAC,oBAAoB,CAAC;AAC9C,MAAMG,IAAI,GAAGH,OAAO,CAAC,QAAQ,CAAC;AAE9B,MAAMI,GAAG,CAAC;EACRC,WAAWA,CAACC,SAAS,EAAEC,MAAM,EAAE;IAC7B,IAAI,CAACC,UAAU,GAAGF,SAAS;IAC3B,IAAI,CAACG,OAAO,GAAGF,MAAM;IACrB,IAAI,CAACG,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,YAAY,GAAG,CAAC;EACvB;;EAEA;EACA,IAAIL,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACE,OAAO;EACrB;EAEA,IAAIH,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAACE,UAAU;EACxB;;EAEA;EACA;EACAK,MAAMA,CAAA,EAAG;IACP,IAAI,CAACL,UAAU,CAACM,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;EACpC;;EAEA;EACAC,OAAOA,CAAA,EAAG;IACR,OAAO,IAAI,CAACP,UAAU;IACtB,OAAO,IAAI,CAACE,MAAM;IAClB,OAAO,IAAI,CAACC,KAAK;EACnB;EAEAK,QAAQA,CAACC,SAAS,EAAE;IAClB,OAAO,IAAI,CAACP,MAAM,CAACO,SAAS,GAAG,CAAC,CAAC;EACnC;;EAEA;EACAC,SAASA,CAACC,OAAO,EAAE;IACjB,IAAIC,IAAI,GAAG,IAAI,CAACV,MAAM,CAACS,OAAO,CAACE,GAAG,GAAG,CAAC,CAAC;IACvC,IAAI,CAACD,IAAI,EAAE;MACT,MAAME,MAAM,GAAG,IAAI,CAACd,UAAU,CAACe,SAAS,CAACJ,OAAO,CAACE,GAAG,CAAC;MACrDD,IAAI,GAAG,IAAIjB,IAAI,CAAC,IAAI,EAAEmB,MAAM,EAAEH,OAAO,CAACA,OAAO,CAAC;MAC9C,IAAI,CAACT,MAAM,CAACS,OAAO,CAACE,GAAG,GAAG,CAAC,CAAC,GAAGD,IAAI;IACrC;IACA,OAAOA,IAAI;EACb;;EAEA;EACAI,OAAOA,CAACH,GAAG,EAAE;IACX,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAC3B;MACA,MAAMC,MAAM,GAAG,IAAI,CAACd,UAAU,CAACiB,YAAY,CAACJ,GAAG,CAAC;MAChD,IAAIC,MAAM,EAAE;QACVD,GAAG,GAAGC,MAAM,CAACf,MAAM;MACrB,CAAC,MAAM;QACLc,GAAG,GAAGnB,QAAQ,CAACwB,GAAG,CAACL,GAAG,CAAC;MACzB;IACF;IACA,OACE,IAAI,CAACX,MAAM,CAACW,GAAG,GAAG,CAAC,CAAC,IACpB,IAAI,CAACH,SAAS,CAAC;MACbC,OAAO,EAAEjB,QAAQ,CAACyB,aAAa,CAAC,IAAI,CAAClB,OAAO,EAAEY,GAAG,CAAC;MAClDO,GAAG,EAAE,IAAI,CAACnB,OAAO;MACjBY;IACF,CAAC,CAAC;EAEN;;EAEA;EACAQ,MAAMA,CAACC,KAAK,EAAEC,KAAK,EAAc;IAC/B,MAAMC,KAAK,GAAGF,KAAK,GAAGC,KAAK;IAAC,SAAAE,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADNC,OAAO,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAPF,OAAO,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;IAAA;IAE7B,MAAMC,OAAO,GAAGH,OAAO,CAACD,MAAM,GAAGJ,KAAK;IACtC,MAAMS,IAAI,GAAG,IAAI,CAAC9B,MAAM,CAACyB,MAAM;IAC/B,IAAIM,CAAC;IACL,IAAIC,IAAI;IACR,IAAIC,IAAI;IAER,IAAIJ,OAAO,GAAG,CAAC,EAAE;MACf;MACA,KAAKE,CAAC,GAAGX,KAAK,GAAGM,OAAO,CAACD,MAAM,EAAEM,CAAC,IAAID,IAAI,EAAEC,CAAC,EAAE,EAAE;QAC/CE,IAAI,GAAG,IAAI,CAACjC,MAAM,CAAC+B,CAAC,GAAG,CAAC,CAAC;QACzBC,IAAI,GAAG,IAAI,CAAChC,MAAM,CAAC+B,CAAC,GAAGF,OAAO,GAAG,CAAC,CAAC;QACnC,IAAIG,IAAI,EAAE;UACRC,IAAI,GAAG,IAAI,CAACnB,OAAO,CAACiB,CAAC,CAAC;UACtBE,IAAI,CAACC,KAAK,GAAGF,IAAI,CAACE,KAAK;UACvBD,IAAI,CAAChC,KAAK,GAAG+B,IAAI,CAAC/B,KAAK;UACvB;UACAgC,IAAI,CAACE,QAAQ,GAAGH,IAAI,CAACG,QAAQ;QAC/B,CAAC,MAAM,IAAIF,IAAI,EAAE;UACfA,IAAI,CAACC,KAAK,GAAG,IAAI;UACjBD,IAAI,CAAChC,KAAK,GAAG,CAAC,CAAC;UACf;UACAgC,IAAI,CAACE,QAAQ,GAAGC,SAAS;QAC3B;MACF;IACF,CAAC,MAAM,IAAIP,OAAO,GAAG,CAAC,EAAE;MACtB;MACA,KAAKE,CAAC,GAAGD,IAAI,EAAEC,CAAC,IAAIT,KAAK,EAAES,CAAC,EAAE,EAAE;QAC9BC,IAAI,GAAG,IAAI,CAAChC,MAAM,CAAC+B,CAAC,GAAG,CAAC,CAAC;QACzB,IAAIC,IAAI,EAAE;UACRC,IAAI,GAAG,IAAI,CAACnB,OAAO,CAACiB,CAAC,GAAGF,OAAO,CAAC;UAChCI,IAAI,CAACC,KAAK,GAAGF,IAAI,CAACE,KAAK;UACvBD,IAAI,CAAChC,KAAK,GAAG+B,IAAI,CAAC/B,KAAK;UACvB;UACAgC,IAAI,CAACE,QAAQ,GAAGH,IAAI,CAACG,QAAQ;QAC/B,CAAC,MAAM;UACL,IAAI,CAACnC,MAAM,CAAC+B,CAAC,GAAGF,OAAO,GAAG,CAAC,CAAC,GAAGO,SAAS;QAC1C;MACF;IACF;;IAEA;IACA,KAAKL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,OAAO,CAACD,MAAM,EAAEM,CAAC,EAAE,EAAE;MACnCE,IAAI,GAAG,IAAI,CAACnB,OAAO,CAACM,KAAK,GAAGW,CAAC,CAAC;MAC9BE,IAAI,CAACC,KAAK,GAAGR,OAAO,CAACK,CAAC,CAAC;MACvBE,IAAI,CAAChC,KAAK,GAAG,CAAC,CAAC;MACf;MACAgC,IAAI,CAACE,QAAQ,GAAGC,SAAS;IAC3B;EACF;;EAEA;EACAC,QAAQA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IAC1B,IAAI,CAACA,QAAQ,EAAE;MACbA,QAAQ,GAAGD,OAAO;MAClBA,OAAO,GAAG,IAAI;IAChB;IACA,IAAIA,OAAO,IAAIA,OAAO,CAACE,YAAY,EAAE;MACnC,MAAMC,CAAC,GAAG,IAAI,CAACzC,MAAM,CAACyB,MAAM;MAC5B,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIU,CAAC,EAAEV,CAAC,EAAE,EAAE;QAC3BQ,QAAQ,CAAC,IAAI,CAACzB,OAAO,CAACiB,CAAC,CAAC,EAAEA,CAAC,CAAC;MAC9B;IACF,CAAC,MAAM;MACL,IAAI,CAAC/B,MAAM,CAAC0C,OAAO,CAAC,CAAChC,IAAI,EAAEiC,KAAK,KAAK;QACnC,IAAIjC,IAAI,IAAIA,IAAI,CAACkC,IAAI,KAAKrD,KAAK,CAACsD,SAAS,CAACC,IAAI,EAAE;UAC9CP,QAAQ,CAAC7B,IAAI,EAAEiC,KAAK,GAAG,CAAC,CAAC;QAC3B;MACF,CAAC,CAAC;IACJ;EACF;;EAEA;EACA;EACAI,YAAYA,CAACC,GAAG,EAAEC,IAAI,EAAE;IACtB,MAAMC,EAAE,GAAG,IAAI,CAACpD,UAAU;IAC1B,MAAMqD,IAAI,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC;IACtC,MAAMM,KAAK,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,IAAI,GAAG,CAAC,CAAC,IAAI,KAAK;IAC5C,MAAMM,EAAE,GAAG;MACTC,EAAE,EAAE,IAAI,CAACzD,OAAO;MAChBsD,GAAG,EAAEC,KAAK;MACVG,GAAG,EAAE;IACP,CAAC;IACD,IAAIN,IAAI,EAAEI,EAAE,CAACG,GAAG,GAAGP,IAAI;IAEvBD,EAAE,CAACS,SAAS,CAACC,IAAI,CAACL,EAAE,CAAC;EACvB;;EAEA;EACA,IAAIM,MAAMA,CAAA,EAAG;IACX,MAAMA,MAAM,GAAG,EAAE;IACjB,IAAI,CAAC7D,MAAM,CAAC0C,OAAO,CAAChC,IAAI,IAAI;MAC1B,IAAIA,IAAI,IAAIA,IAAI,CAACkC,IAAI,KAAKrD,KAAK,CAACsD,SAAS,CAACC,IAAI,EAAE;QAC9Ce,MAAM,CAACnD,IAAI,CAACC,GAAG,CAAC,GAAGD,IAAI,CAACwB,KAAK;MAC/B;IACF,CAAC,CAAC;IACF,OAAO2B,MAAM;EACf;;EAEA;EACA,IAAIA,MAAMA,CAAC3B,KAAK,EAAE;IAChB;IACA,IAAI,CAAClC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACkC,KAAK,EAAE;MACV;IAAA,CACD,MAAM,IAAIA,KAAK,YAAYP,KAAK,EAAE;MACjC,IAAImC,MAAM,GAAG,CAAC;MACd,IAAI5B,KAAK,CAAC6B,cAAc,CAAC,GAAG,CAAC,EAAE;QAC7B;QACAD,MAAM,GAAG,CAAC;MACZ;MACA5B,KAAK,CAACQ,OAAO,CAAC,CAACsB,IAAI,EAAErB,KAAK,KAAK;QAC7B,IAAIqB,IAAI,KAAK5B,SAAS,EAAE;UACtB,IAAI,CAAC5B,SAAS,CAAC;YACbC,OAAO,EAAEjB,QAAQ,CAACyB,aAAa,CAAC,IAAI,CAAClB,OAAO,EAAE4C,KAAK,GAAGmB,MAAM,CAAC;YAC7D5C,GAAG,EAAE,IAAI,CAACnB,OAAO;YACjBY,GAAG,EAAEgC,KAAK,GAAGmB;UACf,CAAC,CAAC,CAAC5B,KAAK,GAAG8B,IAAI;QACjB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,CAAClE,UAAU,CAACmE,aAAa,CAAC,CAACrD,MAAM,EAAEsD,GAAG,KAAK;QAC7C,IAAIhC,KAAK,CAACgC,GAAG,CAAC,KAAK9B,SAAS,EAAE;UAC5B,IAAI,CAAC5B,SAAS,CAAC;YACbC,OAAO,EAAEjB,QAAQ,CAACyB,aAAa,CAAC,IAAI,CAAClB,OAAO,EAAEa,MAAM,CAACf,MAAM,CAAC;YAC5DqB,GAAG,EAAE,IAAI,CAACnB,OAAO;YACjBY,GAAG,EAAEC,MAAM,CAACf;UACd,CAAC,CAAC,CAACqC,KAAK,GAAGA,KAAK,CAACgC,GAAG,CAAC;QACvB;MACF,CAAC,CAAC;IACJ;EACF;;EAEA;EACA,IAAIC,SAASA,CAAA,EAAG;IACd,OAAO9E,CAAC,CAAC+E,IAAI,CAAC,IAAI,CAACpE,MAAM,EAAEU,IAAI,IAAIA,IAAI,IAAIA,IAAI,CAACkC,IAAI,KAAKrD,KAAK,CAACsD,SAAS,CAACC,IAAI,CAAC;EAChF;EAEA,IAAIuB,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAACrE,MAAM,CAACyB,MAAM;EAC3B;EAEA,IAAI6C,eAAeA,CAAA,EAAG;IACpB,IAAIjD,KAAK,GAAG,CAAC;IACb,IAAI,CAACgB,QAAQ,CAAC,MAAM;MAClBhB,KAAK,EAAE;IACT,CAAC,CAAC;IACF,OAAOA,KAAK;EACd;;EAEA;EACA,IAAIkD,UAAUA,CAAA,EAAG;IACf,IAAIb,GAAG,GAAG,CAAC;IACX,IAAIL,GAAG,GAAG,CAAC;IACX,IAAI,CAACrD,MAAM,CAAC0C,OAAO,CAAChC,IAAI,IAAI;MAC1B,IAAIA,IAAI,IAAIA,IAAI,CAACkC,IAAI,KAAKrD,KAAK,CAACsD,SAAS,CAACC,IAAI,EAAE;QAC9C,IAAI,CAACY,GAAG,IAAIA,GAAG,GAAGhD,IAAI,CAACC,GAAG,EAAE;UAC1B+C,GAAG,GAAGhD,IAAI,CAACC,GAAG;QAChB;QACA,IAAI0C,GAAG,GAAG3C,IAAI,CAACC,GAAG,EAAE;UAClB0C,GAAG,GAAG3C,IAAI,CAACC,GAAG;QAChB;MACF;IACF,CAAC,CAAC;IACF,OAAO+C,GAAG,GAAG,CAAC,GACV;MACEA,GAAG;MACHL;IACF,CAAC,GACD,IAAI;EACV;;EAEA;EACA;EACAmB,WAAWA,CAACC,IAAI,EAAEvC,KAAK,EAAE;IACvB,IAAI,CAACjC,KAAK,CAACwE,IAAI,CAAC,GAAGvC,KAAK;IACxB,IAAI,CAAClC,MAAM,CAAC0C,OAAO,CAAChC,IAAI,IAAI;MAC1B,IAAIA,IAAI,EAAE;QACRA,IAAI,CAAC+D,IAAI,CAAC,GAAGvC,KAAK;MACpB;IACF,CAAC,CAAC;IACF,OAAOA,KAAK;EACd;EAEA,IAAIwC,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACzE,KAAK,CAACyE,MAAM;EAC1B;EAEA,IAAIA,MAAMA,CAACxC,KAAK,EAAE;IAChB,IAAI,CAACsC,WAAW,CAAC,QAAQ,EAAEtC,KAAK,CAAC;EACnC;EAEA,IAAIyC,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC1E,KAAK,CAAC0E,IAAI;EACxB;EAEA,IAAIA,IAAIA,CAACzC,KAAK,EAAE;IACd,IAAI,CAACsC,WAAW,CAAC,MAAM,EAAEtC,KAAK,CAAC;EACjC;EAEA,IAAI0C,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC3E,KAAK,CAAC2E,SAAS;EAC7B;EAEA,IAAIA,SAASA,CAAC1C,KAAK,EAAE;IACnB,IAAI,CAACsC,WAAW,CAAC,WAAW,EAAEtC,KAAK,CAAC;EACtC;EAEA,IAAI2C,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC5E,KAAK,CAAC4E,UAAU;EAC9B;EAEA,IAAIA,UAAUA,CAAC3C,KAAK,EAAE;IACpB,IAAI,CAACsC,WAAW,CAAC,YAAY,EAAEtC,KAAK,CAAC;EACvC;EAEA,IAAI4C,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC7E,KAAK,CAAC6E,MAAM;EAC1B;EAEA,IAAIA,MAAMA,CAAC5C,KAAK,EAAE;IAChB,IAAI,CAACsC,WAAW,CAAC,QAAQ,EAAEtC,KAAK,CAAC;EACnC;EAEA,IAAI6C,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC9E,KAAK,CAAC8E,IAAI;EACxB;EAEA,IAAIA,IAAIA,CAAC7C,KAAK,EAAE;IACd,IAAI,CAACsC,WAAW,CAAC,MAAM,EAAEtC,KAAK,CAAC;EACjC;EAEA,IAAI8C,MAAMA,CAAA,EAAG;IACX,OAAO,CAAC,CAAC,IAAI,CAACC,OAAO;EACvB;EAEA,IAAID,MAAMA,CAAC9C,KAAK,EAAE;IAChB,IAAI,CAAC+C,OAAO,GAAG/C,KAAK;EACtB;EAEA,IAAIhC,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACgF,aAAa,IAAI,CAAC;EAChC;EAEA,IAAIhF,YAAYA,CAACgC,KAAK,EAAE;IACtB,IAAI,CAACgD,aAAa,GAAGhD,KAAK;EAC5B;EAEA,IAAIiD,SAASA,CAAA,EAAG;IACd,OAAO,CAAC,EACN,IAAI,CAACD,aAAa,IAAI,IAAI,CAACA,aAAa,IAAI,IAAI,CAACpF,UAAU,CAACsF,UAAU,CAACC,eAAe,CACvF;EACH;;EAEA;EACA,IAAIC,KAAKA,CAAA,EAAG;IACV,MAAMC,KAAK,GAAG,EAAE;IAChB,IAAI7B,GAAG,GAAG,CAAC;IACX,IAAIL,GAAG,GAAG,CAAC;IACX,IAAI,CAACrD,MAAM,CAAC0C,OAAO,CAAChC,IAAI,IAAI;MAC1B,IAAIA,IAAI,EAAE;QACR,MAAM8E,SAAS,GAAG9E,IAAI,CAAC4E,KAAK;QAC5B,IAAIE,SAAS,EAAE;UACb,IAAI,CAAC9B,GAAG,IAAIA,GAAG,GAAGhD,IAAI,CAACC,GAAG,EAAE;YAC1B+C,GAAG,GAAGhD,IAAI,CAACC,GAAG;UAChB;UACA,IAAI0C,GAAG,GAAG3C,IAAI,CAACC,GAAG,EAAE;YAClB0C,GAAG,GAAG3C,IAAI,CAACC,GAAG;UAChB;UACA4E,KAAK,CAAC3B,IAAI,CAAC4B,SAAS,CAAC;QACvB;MACF;IACF,CAAC,CAAC;IAEF,OAAO,IAAI,CAACC,MAAM,IAAIF,KAAK,CAAC9D,MAAM,GAC9B;MACE8D,KAAK;MACL1F,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB6D,GAAG;MACHL,GAAG;MACHoC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBxF,KAAK,EAAE,IAAI,CAACA,KAAK;MACjB+E,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB9E,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BiF,SAAS,EAAE,IAAI,CAACA;IAClB,CAAC,GACD,IAAI;EACV;EAEA,IAAIG,KAAKA,CAACpD,KAAK,EAAE;IACf,IAAIA,KAAK,CAACrC,MAAM,KAAK,IAAI,CAACE,OAAO,EAAE;MACjC,MAAM,IAAI2F,KAAK,CAAC,6BAA6B,CAAC;IAChD;IACA,IAAI,CAAC1F,MAAM,GAAG,EAAE;IAChB,IAAI2F,eAAe;IACnBzD,KAAK,CAACqD,KAAK,CAAC7C,OAAO,CAAC8C,SAAS,IAAI;MAC/B,QAAQA,SAAS,CAAC5C,IAAI;QACpB,KAAKnD,IAAI,CAACmG,KAAK,CAACC,KAAK;UACnB;UACA;QACF;UAAS;YACP,IAAIpF,OAAO;YACX,IAAI+E,SAAS,CAAC/E,OAAO,EAAE;cACrBA,OAAO,GAAGjB,QAAQ,CAACsG,aAAa,CAACN,SAAS,CAAC/E,OAAO,CAAC;YACrD,CAAC,MAAM,IAAIkF,eAAe,EAAE;cAC1B;cACA;cACA,MAAM;gBAACzE;cAAG,CAAC,GAAGyE,eAAe;cAC7B,MAAMhF,GAAG,GAAGgF,eAAe,CAAChF,GAAG,GAAG,CAAC;cACnCF,OAAO,GAAG;gBACRS,GAAG;gBACHP,GAAG;gBACHF,OAAO,EAAEjB,QAAQ,CAACyB,aAAa,CAACC,GAAG,EAAEP,GAAG,CAAC;gBACzCoF,QAAQ,EAAG,IAAGvG,QAAQ,CAACwG,GAAG,CAACrF,GAAG,CAAE,IAAGO,GAAI;cACzC,CAAC;YACH;YACAyE,eAAe,GAAGlF,OAAO;YACzB,MAAMC,IAAI,GAAG,IAAI,CAACF,SAAS,CAACC,OAAO,CAAC;YACpCC,IAAI,CAAC4E,KAAK,GAAGE,SAAS;YACtB;UACF;MACF;IACF,CAAC,CAAC;IAEF,IAAItD,KAAK,CAACuD,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAGvD,KAAK,CAACuD,MAAM;IAC5B,CAAC,MAAM;MACL,OAAO,IAAI,CAACA,MAAM;IACpB;IAEA,IAAI,CAACT,MAAM,GAAG9C,KAAK,CAAC8C,MAAM;IAC1B,IAAI,CAAC9E,YAAY,GAAGgC,KAAK,CAAChC,YAAY,IAAI,CAAC;IAE3C,IAAI,CAACD,KAAK,GAAIiC,KAAK,CAACjC,KAAK,IAAIgG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACjE,KAAK,CAACjC,KAAK,CAAC,CAAC,IAAK,CAAC,CAAC;EAC7E;AACF;AAEAmG,MAAM,CAACC,OAAO,GAAG3G,GAAG"}
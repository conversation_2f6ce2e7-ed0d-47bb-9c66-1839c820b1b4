{"version": 3, "file": "worksheet.js", "names": ["_", "require", "co<PERSON><PERSON><PERSON>", "Range", "Row", "Column", "Enums", "Image", "Table", "DataValidations", "Encryptor", "copyStyle", "Worksheet", "constructor", "options", "_workbook", "workbook", "id", "orderNo", "name", "state", "_rows", "_columns", "_keys", "_merges", "rowBreaks", "properties", "Object", "assign", "defaultRowHeight", "dyDescent", "outlineLevelCol", "outlineLevelRow", "pageSetup", "margins", "left", "right", "top", "bottom", "header", "footer", "orientation", "horizontalDpi", "verticalDpi", "fitToPage", "fitToWidth", "fitToHeight", "scale", "pageOrder", "blackAndWhite", "draft", "cellComments", "errors", "paperSize", "undefined", "showRowColHeaders", "showGridLines", "firstPageNumber", "horizontalCentered", "verticalCentered", "colBreaks", "headerFooter", "differentFirst", "differentOddEven", "<PERSON><PERSON><PERSON><PERSON>", "oddFooter", "<PERSON><PERSON><PERSON><PERSON>", "evenFooter", "firstHeader", "firstFooter", "dataValidations", "views", "autoFilter", "_media", "sheetProtection", "tables", "conditionalFormattings", "_name", "Error", "test", "length", "console", "warn", "substring", "_worksheets", "find", "ws", "toLowerCase", "destroy", "removeWorksheetEx", "dimensions", "for<PERSON>ach", "row", "rowDims", "expand", "number", "min", "max", "columns", "value", "_headerRowCount", "reduce", "pv", "cv", "headerCount", "headers", "Math", "count", "defn", "column", "push", "getColumnKey", "key", "setColumnKey", "deleteColumnKey", "eachColumnKey", "f", "each", "getColumn", "c", "col", "l2n", "n", "spliceColumns", "start", "rows", "nRows", "_len", "arguments", "inserts", "Array", "_key", "i", "rowArguments", "insert", "getRow", "splice", "apply", "r", "nExpand", "nKeep", "nEnd", "definedNames", "lastColumn", "columnCount", "maxCount", "eachRow", "cellCount", "actualColumnCount", "counts", "eachCell", "_ref", "_commitRow", "_lastRowNumber", "_nextRow", "lastRow", "findRow", "findRows", "slice", "rowCount", "actualRowCount", "getRows", "addRow", "style", "rowNo", "values", "_setStyleOption", "addRows", "insertRow", "pos", "spliceRows", "insertRows", "_copyStyle", "src", "dest", "styleEmpty", "rSrc", "rDst", "includeEmpty", "cell", "colNumber", "getCell", "height", "duplicateRow", "row<PERSON>um", "fill", "_len2", "_key2", "nInserts", "_value", "cellToBeMerged", "_row", "_number", "prevMaster", "_master", "newMaster", "_column", "merge", "iteratee", "<PERSON><PERSON><PERSON><PERSON>", "getSheetValues", "find<PERSON>ell", "address", "get<PERSON><PERSON><PERSON>", "getCellEx", "mergeCells", "_len3", "cells", "_key3", "_mergeCellsInternal", "mergeCellsWithoutStyle", "_len4", "_key4", "ignoreStyle", "intersects", "master", "j", "_unMergeMaster", "unmerge", "hasMerges", "some", "Boolean", "unMergeCells", "_len5", "_key5", "type", "ValueType", "<PERSON><PERSON>", "fillFormula", "range", "formula", "results", "shareType", "decoded", "decode", "width", "masterAddress", "encodeAddress", "isShared", "getResult", "isArray", "first", "ref", "result", "sharedFormula", "addImage", "imageId", "model", "getImages", "filter", "m", "addBackgroundImage", "getBackgroundImageId", "image", "protect", "password", "Promise", "resolve", "sheet", "spinCount", "Number", "isFinite", "round", "algorithmName", "saltValue", "randomBytes", "toString", "hashValue", "convertPasswordToHash", "unprotect", "addTable", "table", "getTable", "removeTable", "getTables", "addConditionalFormatting", "cf", "removeConditionalFormatting", "Function", "tabColor", "trace", "media", "map", "medium", "cols", "toModel", "rowModel", "merges", "_parseRows", "_parseMergeCells", "fromModel", "t", "module", "exports"], "sources": ["../../../lib/doc/worksheet.js"], "sourcesContent": ["const _ = require('../utils/under-dash');\n\nconst colCache = require('../utils/col-cache');\nconst Range = require('./range');\nconst Row = require('./row');\nconst Column = require('./column');\nconst Enums = require('./enums');\nconst Image = require('./image');\nconst Table = require('./table');\nconst DataValidations = require('./data-validations');\nconst Encryptor = require('../utils/encryptor');\nconst {copyStyle} = require('../utils/copy-style');\n\n// Worksheet requirements\n//  Operate as sheet inside workbook or standalone\n//  Load and Save from file and stream\n//  Access/Add/Delete individual cells\n//  Manage column widths and row heights\n\nclass Worksheet {\n  constructor(options) {\n    options = options || {};\n    this._workbook = options.workbook;\n\n    // in a workbook, each sheet will have a number\n    this.id = options.id;\n    this.orderNo = options.orderNo;\n\n    // and a name\n    this.name = options.name;\n\n    // add a state\n    this.state = options.state || 'visible';\n\n    // rows allows access organised by row. Sparse array of arrays indexed by row-1, col\n    // Note: _rows is zero based. Must subtract 1 to go from cell.row to index\n    this._rows = [];\n\n    // column definitions\n    this._columns = null;\n\n    // column keys (addRow convenience): key ==> this._collumns index\n    this._keys = {};\n\n    // keep record of all merges\n    this._merges = {};\n\n    // record of all row and column pageBreaks\n    this.rowBreaks = [];\n\n    // for tabColor, default row height, outline levels, etc\n    this.properties = Object.assign(\n      {},\n      {\n        defaultRowHeight: 15,\n        dyDescent: 55,\n        outlineLevelCol: 0,\n        outlineLevelRow: 0,\n      },\n      options.properties\n    );\n\n    // for all things printing\n    this.pageSetup = Object.assign(\n      {},\n      {\n        margins: {left: 0.7, right: 0.7, top: 0.75, bottom: 0.75, header: 0.3, footer: 0.3},\n        orientation: 'portrait',\n        horizontalDpi: 4294967295,\n        verticalDpi: 4294967295,\n        fitToPage: !!(\n          options.pageSetup &&\n          (options.pageSetup.fitToWidth || options.pageSetup.fitToHeight) &&\n          !options.pageSetup.scale\n        ),\n        pageOrder: 'downThenOver',\n        blackAndWhite: false,\n        draft: false,\n        cellComments: 'None',\n        errors: 'displayed',\n        scale: 100,\n        fitToWidth: 1,\n        fitToHeight: 1,\n        paperSize: undefined,\n        showRowColHeaders: false,\n        showGridLines: false,\n        firstPageNumber: undefined,\n        horizontalCentered: false,\n        verticalCentered: false,\n        rowBreaks: null,\n        colBreaks: null,\n      },\n      options.pageSetup\n    );\n\n    this.headerFooter = Object.assign(\n      {},\n      {\n        differentFirst: false,\n        differentOddEven: false,\n        oddHeader: null,\n        oddFooter: null,\n        evenHeader: null,\n        evenFooter: null,\n        firstHeader: null,\n        firstFooter: null,\n      },\n      options.headerFooter\n    );\n\n    this.dataValidations = new DataValidations();\n\n    // for freezepanes, split, zoom, gridlines, etc\n    this.views = options.views || [];\n\n    this.autoFilter = options.autoFilter || null;\n\n    // for images, etc\n    this._media = [];\n\n    // worksheet protection\n    this.sheetProtection = null;\n\n    // for tables\n    this.tables = {};\n\n    this.conditionalFormattings = [];\n  }\n\n  get name() {\n    return this._name;\n  }\n\n  set name(name) {\n    if (name === undefined) {\n      name = `sheet${this.id}`;\n    }\n\n    if (this._name === name) return;\n\n    if (typeof name !== 'string') {\n      throw new Error('The name has to be a string.');\n    }\n\n    if (name === '') {\n      throw new Error('The name can\\'t be empty.');\n    }\n\n    if (name === 'History') {\n      throw new Error('The name \"History\" is protected. Please use a different name.');\n    }\n\n    // Illegal character in worksheet name: asterisk (*), question mark (?),\n    // colon (:), forward slash (/ \\), or bracket ([])\n    if (/[*?:/\\\\[\\]]/.test(name)) {\n      throw new Error(`Worksheet name ${name} cannot include any of the following characters: * ? : \\\\ / [ ]`);\n    }\n\n    if (/(^')|('$)/.test(name)) {\n      throw new Error(`The first or last character of worksheet name cannot be a single quotation mark: ${name}`);\n    }\n\n    if (name && name.length > 31) {\n      // eslint-disable-next-line no-console\n      console.warn(`Worksheet name ${name} exceeds 31 chars. This will be truncated`);\n      name = name.substring(0, 31);\n    }\n\n    if (this._workbook._worksheets.find(ws => ws && ws.name.toLowerCase() === name.toLowerCase())) {\n      throw new Error(`Worksheet name already exists: ${name}`);\n    }\n\n    this._name = name;\n  }\n\n  get workbook() {\n    return this._workbook;\n  }\n\n  // when you're done with this worksheet, call this to remove from workbook\n  destroy() {\n    this._workbook.removeWorksheetEx(this);\n  }\n\n  // Get the bounding range of the cells in this worksheet\n  get dimensions() {\n    const dimensions = new Range();\n    this._rows.forEach(row => {\n      if (row) {\n        const rowDims = row.dimensions;\n        if (rowDims) {\n          dimensions.expand(row.number, rowDims.min, row.number, rowDims.max);\n        }\n      }\n    });\n    return dimensions;\n  }\n\n  // =========================================================================\n  // Columns\n\n  // get the current columns array.\n  get columns() {\n    return this._columns;\n  }\n\n  // set the columns from an array of column definitions.\n  // Note: any headers defined will overwrite existing values.\n  set columns(value) {\n    // calculate max header row count\n    this._headerRowCount = value.reduce((pv, cv) => {\n      const headerCount = (cv.header && 1) || (cv.headers && cv.headers.length) || 0;\n      return Math.max(pv, headerCount);\n    }, 0);\n\n    // construct Column objects\n    let count = 1;\n    const columns = (this._columns = []);\n    value.forEach(defn => {\n      const column = new Column(this, count++, false);\n      columns.push(column);\n      column.defn = defn;\n    });\n  }\n\n  getColumnKey(key) {\n    return this._keys[key];\n  }\n\n  setColumnKey(key, value) {\n    this._keys[key] = value;\n  }\n\n  deleteColumnKey(key) {\n    delete this._keys[key];\n  }\n\n  eachColumnKey(f) {\n    _.each(this._keys, f);\n  }\n\n  // get a single column by col number. If it doesn't exist, create it and any gaps before it\n  getColumn(c) {\n    if (typeof c === 'string') {\n      // if it matches a key'd column, return that\n      const col = this._keys[c];\n      if (col) return col;\n\n      // otherwise, assume letter\n      c = colCache.l2n(c);\n    }\n    if (!this._columns) {\n      this._columns = [];\n    }\n    if (c > this._columns.length) {\n      let n = this._columns.length + 1;\n      while (n <= c) {\n        this._columns.push(new Column(this, n++));\n      }\n    }\n    return this._columns[c - 1];\n  }\n\n  spliceColumns(start, count, ...inserts) {\n    const rows = this._rows;\n    const nRows = rows.length;\n    if (inserts.length > 0) {\n      // must iterate over all rows whether they exist yet or not\n      for (let i = 0; i < nRows; i++) {\n        const rowArguments = [start, count];\n        // eslint-disable-next-line no-loop-func\n        inserts.forEach(insert => {\n          rowArguments.push(insert[i] || null);\n        });\n        const row = this.getRow(i + 1);\n        // eslint-disable-next-line prefer-spread\n        row.splice.apply(row, rowArguments);\n      }\n    } else {\n      // nothing to insert, so just splice all rows\n      this._rows.forEach(r => {\n        if (r) {\n          r.splice(start, count);\n        }\n      });\n    }\n\n    // splice column definitions\n    const nExpand = inserts.length - count;\n    const nKeep = start + count;\n    const nEnd = this._columns.length;\n    if (nExpand < 0) {\n      for (let i = start + inserts.length; i <= nEnd; i++) {\n        this.getColumn(i).defn = this.getColumn(i - nExpand).defn;\n      }\n    } else if (nExpand > 0) {\n      for (let i = nEnd; i >= nKeep; i--) {\n        this.getColumn(i + nExpand).defn = this.getColumn(i).defn;\n      }\n    }\n    for (let i = start; i < start + inserts.length; i++) {\n      this.getColumn(i).defn = null;\n    }\n\n    // account for defined names\n    this.workbook.definedNames.spliceColumns(this.name, start, count, inserts.length);\n  }\n\n  get lastColumn() {\n    return this.getColumn(this.columnCount);\n  }\n\n  get columnCount() {\n    let maxCount = 0;\n    this.eachRow(row => {\n      maxCount = Math.max(maxCount, row.cellCount);\n    });\n    return maxCount;\n  }\n\n  get actualColumnCount() {\n    // performance nightmare - for each row, counts all the columns used\n    const counts = [];\n    let count = 0;\n    this.eachRow(row => {\n      row.eachCell(({col}) => {\n        if (!counts[col]) {\n          counts[col] = true;\n          count++;\n        }\n      });\n    });\n    return count;\n  }\n\n  // =========================================================================\n  // Rows\n\n  _commitRow() {\n    // nop - allows streaming reader to fill a document\n  }\n\n  get _lastRowNumber() {\n    // need to cope with results of splice\n    const rows = this._rows;\n    let n = rows.length;\n    while (n > 0 && rows[n - 1] === undefined) {\n      n--;\n    }\n    return n;\n  }\n\n  get _nextRow() {\n    return this._lastRowNumber + 1;\n  }\n\n  get lastRow() {\n    if (this._rows.length) {\n      return this._rows[this._rows.length - 1];\n    }\n    return undefined;\n  }\n\n  // find a row (if exists) by row number\n  findRow(r) {\n    return this._rows[r - 1];\n  }\n\n  // find multiple rows (if exists) by row number\n  findRows(start, length) {\n    return this._rows.slice(start - 1, start - 1 + length);\n  }\n\n  get rowCount() {\n    return this._lastRowNumber;\n  }\n\n  get actualRowCount() {\n    // counts actual rows that have actual data\n    let count = 0;\n    this.eachRow(() => {\n      count++;\n    });\n    return count;\n  }\n\n  // get a row by row number.\n  getRow(r) {\n    let row = this._rows[r - 1];\n    if (!row) {\n      row = this._rows[r - 1] = new Row(this, r);\n    }\n    return row;\n  }\n\n  // get multiple rows by row number.\n  getRows(start, length) {\n    if (length < 1) return undefined;\n    const rows = [];\n    for (let i = start; i < start + length; i++) {\n      rows.push(this.getRow(i));\n    }\n    return rows;\n  }\n\n  addRow(value, style = 'n') {\n    const rowNo = this._nextRow;\n    const row = this.getRow(rowNo);\n    row.values = value;\n    this._setStyleOption(rowNo, style[0] === 'i' ? style : 'n');\n    return row;\n  }\n\n  addRows(value, style = 'n') {\n    const rows = [];\n    value.forEach(row => {\n      rows.push(this.addRow(row, style));\n    });\n    return rows;\n  }\n\n  insertRow(pos, value, style = 'n') {\n    this.spliceRows(pos, 0, value);\n    this._setStyleOption(pos, style);\n    return this.getRow(pos);\n  }\n\n  insertRows(pos, values, style = 'n') {\n    this.spliceRows(pos, 0, ...values);\n    if (style !== 'n') {\n      // copy over the styles\n      for (let i = 0; i < values.length; i++) {\n        if (style[0] === 'o' && this.findRow(values.length + pos + i) !== undefined) {\n          this._copyStyle(values.length + pos + i, pos + i, style[1] === '+');\n        } else if (style[0] === 'i' && this.findRow(pos - 1) !== undefined) {\n          this._copyStyle(pos - 1, pos + i, style[1] === '+');\n        }\n      }\n    }\n    return this.getRows(pos, values.length);\n  }\n\n  // set row at position to same style as of either pervious row (option 'i') or next row (option 'o')\n  _setStyleOption(pos, style = 'n') {\n    if (style[0] === 'o' && this.findRow(pos + 1) !== undefined) {\n      this._copyStyle(pos + 1, pos, style[1] === '+');\n    } else if (style[0] === 'i' && this.findRow(pos - 1) !== undefined) {\n      this._copyStyle(pos - 1, pos, style[1] === '+');\n    }\n  }\n\n  _copyStyle(src, dest, styleEmpty = false) {\n    const rSrc = this.getRow(src);\n    const rDst = this.getRow(dest);\n    rDst.style = copyStyle(rSrc.style);\n    // eslint-disable-next-line no-loop-func\n    rSrc.eachCell({includeEmpty: styleEmpty}, (cell, colNumber) => {\n      rDst.getCell(colNumber).style = copyStyle(cell.style);\n    });\n    rDst.height = rSrc.height;\n  }\n\n  duplicateRow(rowNum, count, insert = false) {\n    // create count duplicates of rowNum\n    // either inserting new or overwriting existing rows\n\n    const rSrc = this._rows[rowNum - 1];\n    const inserts = new Array(count).fill(rSrc.values);\n    this.spliceRows(rowNum + 1, insert ? 0 : count, ...inserts);\n\n    // now copy styles...\n    for (let i = 0; i < count; i++) {\n      const rDst = this._rows[rowNum + i];\n      rDst.style = rSrc.style;\n      rDst.height = rSrc.height;\n      // eslint-disable-next-line no-loop-func\n      rSrc.eachCell({includeEmpty: true}, (cell, colNumber) => {\n        rDst.getCell(colNumber).style = cell.style;\n      });\n    }\n  }\n\n  spliceRows(start, count, ...inserts) {\n    // same problem as row.splice, except worse.\n    const nKeep = start + count;\n    const nInserts = inserts.length;\n    const nExpand = nInserts - count;\n    const nEnd = this._rows.length;\n    let i;\n    let rSrc;\n    if (nExpand < 0) {\n      // remove rows\n      if (start === nEnd) {\n        this._rows[nEnd - 1] = undefined;\n      }\n      for (i = nKeep; i <= nEnd; i++) {\n        rSrc = this._rows[i - 1];\n        if (rSrc) {\n          const rDst = this.getRow(i + nExpand);\n          rDst.values = rSrc.values;\n          rDst.style = rSrc.style;\n          rDst.height = rSrc.height;\n          // eslint-disable-next-line no-loop-func\n          rSrc.eachCell({includeEmpty: true}, (cell, colNumber) => {\n            rDst.getCell(colNumber).style = cell.style;\n          });\n          this._rows[i - 1] = undefined;\n        } else {\n          this._rows[i + nExpand - 1] = undefined;\n        }\n      }\n    } else if (nExpand > 0) {\n      // insert new cells\n      for (i = nEnd; i >= nKeep; i--) {\n        rSrc = this._rows[i - 1];\n        if (rSrc) {\n          const rDst = this.getRow(i + nExpand);\n          rDst.values = rSrc.values;\n          rDst.style = rSrc.style;\n          rDst.height = rSrc.height;\n          // eslint-disable-next-line no-loop-func\n          rSrc.eachCell({includeEmpty: true}, (cell, colNumber) => {\n            rDst.getCell(colNumber).style = cell.style;\n\n            // remerge cells accounting for insert offset\n            if (cell._value.constructor.name === 'MergeValue') {\n              const cellToBeMerged = this.getRow(cell._row._number + nInserts).getCell(colNumber);\n              const prevMaster = cell._value._master;\n              const newMaster = this.getRow(prevMaster._row._number + nInserts).getCell(prevMaster._column._number);\n              cellToBeMerged.merge(newMaster);\n            }\n          });\n        } else {\n          this._rows[i + nExpand - 1] = undefined;\n        }\n      }\n    }\n\n    // now copy over the new values\n    for (i = 0; i < nInserts; i++) {\n      const rDst = this.getRow(start + i);\n      rDst.style = {};\n      rDst.values = inserts[i];\n    }\n\n    // account for defined names\n    this.workbook.definedNames.spliceRows(this.name, start, count, nInserts);\n  }\n\n  // iterate over every row in the worksheet, including maybe empty rows\n  eachRow(options, iteratee) {\n    if (!iteratee) {\n      iteratee = options;\n      options = undefined;\n    }\n    if (options && options.includeEmpty) {\n      const n = this._rows.length;\n      for (let i = 1; i <= n; i++) {\n        iteratee(this.getRow(i), i);\n      }\n    } else {\n      this._rows.forEach(row => {\n        if (row && row.hasValues) {\n          iteratee(row, row.number);\n        }\n      });\n    }\n  }\n\n  // return all rows as sparse array\n  getSheetValues() {\n    const rows = [];\n    this._rows.forEach(row => {\n      if (row) {\n        rows[row.number] = row.values;\n      }\n    });\n    return rows;\n  }\n\n  // =========================================================================\n  // Cells\n\n  // returns the cell at [r,c] or address given by r. If not found, return undefined\n  findCell(r, c) {\n    const address = colCache.getAddress(r, c);\n    const row = this._rows[address.row - 1];\n    return row ? row.findCell(address.col) : undefined;\n  }\n\n  // return the cell at [r,c] or address given by r. If not found, create a new one.\n  getCell(r, c) {\n    const address = colCache.getAddress(r, c);\n    const row = this.getRow(address.row);\n    return row.getCellEx(address);\n  }\n\n  // =========================================================================\n  // Merge\n\n  // convert the range defined by ['tl:br'], [tl,br] or [t,l,b,r] into a single 'merged' cell\n  mergeCells(...cells) {\n    const dimensions = new Range(cells);\n    this._mergeCellsInternal(dimensions);\n  }\n\n  mergeCellsWithoutStyle(...cells) {\n    const dimensions = new Range(cells);\n    this._mergeCellsInternal(dimensions, true);\n  }\n\n  _mergeCellsInternal(dimensions, ignoreStyle) {\n    // check cells aren't already merged\n    _.each(this._merges, merge => {\n      if (merge.intersects(dimensions)) {\n        throw new Error('Cannot merge already merged cells');\n      }\n    });\n\n    // apply merge\n    const master = this.getCell(dimensions.top, dimensions.left);\n    for (let i = dimensions.top; i <= dimensions.bottom; i++) {\n      for (let j = dimensions.left; j <= dimensions.right; j++) {\n        // merge all but the master cell\n        if (i > dimensions.top || j > dimensions.left) {\n          this.getCell(i, j).merge(master, ignoreStyle);\n        }\n      }\n    }\n\n    // index merge\n    this._merges[master.address] = dimensions;\n  }\n\n  _unMergeMaster(master) {\n    // master is always top left of a rectangle\n    const merge = this._merges[master.address];\n    if (merge) {\n      for (let i = merge.top; i <= merge.bottom; i++) {\n        for (let j = merge.left; j <= merge.right; j++) {\n          this.getCell(i, j).unmerge();\n        }\n      }\n      delete this._merges[master.address];\n    }\n  }\n\n  get hasMerges() {\n    // return true if this._merges has a merge object\n    return _.some(this._merges, Boolean);\n  }\n\n  // scan the range defined by ['tl:br'], [tl,br] or [t,l,b,r] and if any cell is part of a merge,\n  // un-merge the group. Note this function can affect multiple merges and merge-blocks are\n  // atomic - either they're all merged or all un-merged.\n  unMergeCells(...cells) {\n    const dimensions = new Range(cells);\n\n    // find any cells in that range and unmerge them\n    for (let i = dimensions.top; i <= dimensions.bottom; i++) {\n      for (let j = dimensions.left; j <= dimensions.right; j++) {\n        const cell = this.findCell(i, j);\n        if (cell) {\n          if (cell.type === Enums.ValueType.Merge) {\n            // this cell merges to another master\n            this._unMergeMaster(cell.master);\n          } else if (this._merges[cell.address]) {\n            // this cell is a master\n            this._unMergeMaster(cell);\n          }\n        }\n      }\n    }\n  }\n\n  // ===========================================================================\n  // Shared/Array Formula\n  fillFormula(range, formula, results, shareType = 'shared') {\n    // Define formula for top-left cell and share to rest\n    const decoded = colCache.decode(range);\n    const {top, left, bottom, right} = decoded;\n    const width = right - left + 1;\n    const masterAddress = colCache.encodeAddress(top, left);\n    const isShared = shareType === 'shared';\n\n    // work out result accessor\n    let getResult;\n    if (typeof results === 'function') {\n      getResult = results;\n    } else if (Array.isArray(results)) {\n      if (Array.isArray(results[0])) {\n        getResult = (row, col) => results[row - top][col - left];\n      } else {\n        // eslint-disable-next-line no-mixed-operators\n        getResult = (row, col) => results[(row - top) * width + (col - left)];\n      }\n    } else {\n      getResult = () => undefined;\n    }\n    let first = true;\n    for (let r = top; r <= bottom; r++) {\n      for (let c = left; c <= right; c++) {\n        if (first) {\n          this.getCell(r, c).value = {\n            shareType,\n            formula,\n            ref: range,\n            result: getResult(r, c),\n          };\n          first = false;\n        } else {\n          this.getCell(r, c).value = isShared\n            ? {\n                sharedFormula: masterAddress,\n                result: getResult(r, c),\n              }\n            : getResult(r, c);\n        }\n      }\n    }\n  }\n\n  // =========================================================================\n  // Images\n  addImage(imageId, range) {\n    const model = {\n      type: 'image',\n      imageId,\n      range,\n    };\n    this._media.push(new Image(this, model));\n  }\n\n  getImages() {\n    return this._media.filter(m => m.type === 'image');\n  }\n\n  addBackgroundImage(imageId) {\n    const model = {\n      type: 'background',\n      imageId,\n    };\n    this._media.push(new Image(this, model));\n  }\n\n  getBackgroundImageId() {\n    const image = this._media.find(m => m.type === 'background');\n    return image && image.imageId;\n  }\n\n  // =========================================================================\n  // Worksheet Protection\n  protect(password, options) {\n    // TODO: make this function truly async\n    // perhaps marshal to worker thread or something\n    return new Promise(resolve => {\n      this.sheetProtection = {\n        sheet: true,\n      };\n      if (options && 'spinCount' in options) {\n        // force spinCount to be integer >= 0\n        options.spinCount = Number.isFinite(options.spinCount) ? Math.round(Math.max(0, options.spinCount)) : 100000;\n      }\n      if (password) {\n        this.sheetProtection.algorithmName = 'SHA-512';\n        this.sheetProtection.saltValue = Encryptor.randomBytes(16).toString('base64');\n        this.sheetProtection.spinCount = options && 'spinCount' in options ? options.spinCount : 100000; // allow user specified spinCount\n        this.sheetProtection.hashValue = Encryptor.convertPasswordToHash(\n          password,\n          'SHA512',\n          this.sheetProtection.saltValue,\n          this.sheetProtection.spinCount\n        );\n      }\n      if (options) {\n        this.sheetProtection = Object.assign(this.sheetProtection, options);\n        if (!password && 'spinCount' in options) {\n          delete this.sheetProtection.spinCount;\n        }\n      }\n      resolve();\n    });\n  }\n\n  unprotect() {\n    this.sheetProtection = null;\n  }\n\n  // =========================================================================\n  // Tables\n  addTable(model) {\n    const table = new Table(this, model);\n    this.tables[model.name] = table;\n    return table;\n  }\n\n  getTable(name) {\n    return this.tables[name];\n  }\n\n  removeTable(name) {\n    delete this.tables[name];\n  }\n\n  getTables() {\n    return Object.values(this.tables);\n  }\n\n  // ===========================================================================\n  // Conditional Formatting\n  addConditionalFormatting(cf) {\n    this.conditionalFormattings.push(cf);\n  }\n\n  removeConditionalFormatting(filter) {\n    if (typeof filter === 'number') {\n      this.conditionalFormattings.splice(filter, 1);\n    } else if (filter instanceof Function) {\n      this.conditionalFormattings = this.conditionalFormattings.filter(filter);\n    } else {\n      this.conditionalFormattings = [];\n    }\n  }\n\n  // ===========================================================================\n  // Deprecated\n  get tabColor() {\n    // eslint-disable-next-line no-console\n    console.trace('worksheet.tabColor property is now deprecated. Please use worksheet.properties.tabColor');\n    return this.properties.tabColor;\n  }\n\n  set tabColor(value) {\n    // eslint-disable-next-line no-console\n    console.trace('worksheet.tabColor property is now deprecated. Please use worksheet.properties.tabColor');\n    this.properties.tabColor = value;\n  }\n\n  // ===========================================================================\n  // Model\n\n  get model() {\n    const model = {\n      id: this.id,\n      name: this.name,\n      dataValidations: this.dataValidations.model,\n      properties: this.properties,\n      state: this.state,\n      pageSetup: this.pageSetup,\n      headerFooter: this.headerFooter,\n      rowBreaks: this.rowBreaks,\n      views: this.views,\n      autoFilter: this.autoFilter,\n      media: this._media.map(medium => medium.model),\n      sheetProtection: this.sheetProtection,\n      tables: Object.values(this.tables).map(table => table.model),\n      conditionalFormattings: this.conditionalFormattings,\n    };\n\n    // =================================================\n    // columns\n    model.cols = Column.toModel(this.columns);\n\n    // ==========================================================\n    // Rows\n    const rows = (model.rows = []);\n    const dimensions = (model.dimensions = new Range());\n    this._rows.forEach(row => {\n      const rowModel = row && row.model;\n      if (rowModel) {\n        dimensions.expand(rowModel.number, rowModel.min, rowModel.number, rowModel.max);\n        rows.push(rowModel);\n      }\n    });\n\n    // ==========================================================\n    // Merges\n    model.merges = [];\n    _.each(this._merges, merge => {\n      model.merges.push(merge.range);\n    });\n\n    return model;\n  }\n\n  _parseRows(model) {\n    this._rows = [];\n    model.rows.forEach(rowModel => {\n      const row = new Row(this, rowModel.number);\n      this._rows[row.number - 1] = row;\n      row.model = rowModel;\n    });\n  }\n\n  _parseMergeCells(model) {\n    _.each(model.mergeCells, merge => {\n      // Do not merge styles when importing an Excel file\n      // since each cell may have different styles intentionally.\n      this.mergeCellsWithoutStyle(merge);\n    });\n  }\n\n  set model(value) {\n    this.name = value.name;\n    this._columns = Column.fromModel(this, value.cols);\n    this._parseRows(value);\n\n    this._parseMergeCells(value);\n    this.dataValidations = new DataValidations(value.dataValidations);\n    this.properties = value.properties;\n    this.pageSetup = value.pageSetup;\n    this.headerFooter = value.headerFooter;\n    this.views = value.views;\n    this.autoFilter = value.autoFilter;\n    this._media = value.media.map(medium => new Image(this, medium));\n    this.sheetProtection = value.sheetProtection;\n    this.tables = value.tables.reduce((tables, table) => {\n      const t = new Table();\n      t.model = table;\n      tables[table.name] = t;\n      return tables;\n    }, {});\n    this.conditionalFormattings = value.conditionalFormattings;\n  }\n}\n\nmodule.exports = Worksheet;\n"], "mappings": ";;AAAA,MAAMA,CAAC,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAExC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AAC9C,MAAME,KAAK,GAAGF,OAAO,CAAC,SAAS,CAAC;AAChC,MAAMG,GAAG,GAAGH,OAAO,CAAC,OAAO,CAAC;AAC5B,MAAMI,MAAM,GAAGJ,OAAO,CAAC,UAAU,CAAC;AAClC,MAAMK,KAAK,GAAGL,OAAO,CAAC,SAAS,CAAC;AAChC,MAAMM,KAAK,GAAGN,OAAO,CAAC,SAAS,CAAC;AAChC,MAAMO,KAAK,GAAGP,OAAO,CAAC,SAAS,CAAC;AAChC,MAAMQ,eAAe,GAAGR,OAAO,CAAC,oBAAoB,CAAC;AACrD,MAAMS,SAAS,GAAGT,OAAO,CAAC,oBAAoB,CAAC;AAC/C,MAAM;EAACU;AAAS,CAAC,GAAGV,OAAO,CAAC,qBAAqB,CAAC;;AAElD;AACA;AACA;AACA;AACA;;AAEA,MAAMW,SAAS,CAAC;EACdC,WAAWA,CAACC,OAAO,EAAE;IACnBA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IACvB,IAAI,CAACC,SAAS,GAAGD,OAAO,CAACE,QAAQ;;IAEjC;IACA,IAAI,CAACC,EAAE,GAAGH,OAAO,CAACG,EAAE;IACpB,IAAI,CAACC,OAAO,GAAGJ,OAAO,CAACI,OAAO;;IAE9B;IACA,IAAI,CAACC,IAAI,GAAGL,OAAO,CAACK,IAAI;;IAExB;IACA,IAAI,CAACC,KAAK,GAAGN,OAAO,CAACM,KAAK,IAAI,SAAS;;IAEvC;IACA;IACA,IAAI,CAACC,KAAK,GAAG,EAAE;;IAEf;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI;;IAEpB;IACA,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;;IAEf;IACA,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAACC,SAAS,GAAG,EAAE;;IAEnB;IACA,IAAI,CAACC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAC7B,CAAC,CAAC,EACF;MACEC,gBAAgB,EAAE,EAAE;MACpBC,SAAS,EAAE,EAAE;MACbC,eAAe,EAAE,CAAC;MAClBC,eAAe,EAAE;IACnB,CAAC,EACDlB,OAAO,CAACY,UACV,CAAC;;IAED;IACA,IAAI,CAACO,SAAS,GAAGN,MAAM,CAACC,MAAM,CAC5B,CAAC,CAAC,EACF;MACEM,OAAO,EAAE;QAACC,IAAI,EAAE,GAAG;QAAEC,KAAK,EAAE,GAAG;QAAEC,GAAG,EAAE,IAAI;QAAEC,MAAM,EAAE,IAAI;QAAEC,MAAM,EAAE,GAAG;QAAEC,MAAM,EAAE;MAAG,CAAC;MACnFC,WAAW,EAAE,UAAU;MACvBC,aAAa,EAAE,UAAU;MACzBC,WAAW,EAAE,UAAU;MACvBC,SAAS,EAAE,CAAC,EACV9B,OAAO,CAACmB,SAAS,KAChBnB,OAAO,CAACmB,SAAS,CAACY,UAAU,IAAI/B,OAAO,CAACmB,SAAS,CAACa,WAAW,CAAC,IAC/D,CAAChC,OAAO,CAACmB,SAAS,CAACc,KAAK,CACzB;MACDC,SAAS,EAAE,cAAc;MACzBC,aAAa,EAAE,KAAK;MACpBC,KAAK,EAAE,KAAK;MACZC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE,WAAW;MACnBL,KAAK,EAAE,GAAG;MACVF,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdO,SAAS,EAAEC,SAAS;MACpBC,iBAAiB,EAAE,KAAK;MACxBC,aAAa,EAAE,KAAK;MACpBC,eAAe,EAAEH,SAAS;MAC1BI,kBAAkB,EAAE,KAAK;MACzBC,gBAAgB,EAAE,KAAK;MACvBlC,SAAS,EAAE,IAAI;MACfmC,SAAS,EAAE;IACb,CAAC,EACD9C,OAAO,CAACmB,SACV,CAAC;IAED,IAAI,CAAC4B,YAAY,GAAGlC,MAAM,CAACC,MAAM,CAC/B,CAAC,CAAC,EACF;MACEkC,cAAc,EAAE,KAAK;MACrBC,gBAAgB,EAAE,KAAK;MACvBC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE;IACf,CAAC,EACDvD,OAAO,CAAC+C,YACV,CAAC;IAED,IAAI,CAACS,eAAe,GAAG,IAAI7D,eAAe,CAAC,CAAC;;IAE5C;IACA,IAAI,CAAC8D,KAAK,GAAGzD,OAAO,CAACyD,KAAK,IAAI,EAAE;IAEhC,IAAI,CAACC,UAAU,GAAG1D,OAAO,CAAC0D,UAAU,IAAI,IAAI;;IAE5C;IACA,IAAI,CAACC,MAAM,GAAG,EAAE;;IAEhB;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;;IAE3B;IACA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAEhB,IAAI,CAACC,sBAAsB,GAAG,EAAE;EAClC;EAEA,IAAIzD,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC0D,KAAK;EACnB;EAEA,IAAI1D,IAAIA,CAACA,IAAI,EAAE;IACb,IAAIA,IAAI,KAAKmC,SAAS,EAAE;MACtBnC,IAAI,GAAI,QAAO,IAAI,CAACF,EAAG,EAAC;IAC1B;IAEA,IAAI,IAAI,CAAC4D,KAAK,KAAK1D,IAAI,EAAE;IAEzB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM,IAAI2D,KAAK,CAAC,8BAA8B,CAAC;IACjD;IAEA,IAAI3D,IAAI,KAAK,EAAE,EAAE;MACf,MAAM,IAAI2D,KAAK,CAAC,2BAA2B,CAAC;IAC9C;IAEA,IAAI3D,IAAI,KAAK,SAAS,EAAE;MACtB,MAAM,IAAI2D,KAAK,CAAC,+DAA+D,CAAC;IAClF;;IAEA;IACA;IACA,IAAI,aAAa,CAACC,IAAI,CAAC5D,IAAI,CAAC,EAAE;MAC5B,MAAM,IAAI2D,KAAK,CAAE,kBAAiB3D,IAAK,iEAAgE,CAAC;IAC1G;IAEA,IAAI,WAAW,CAAC4D,IAAI,CAAC5D,IAAI,CAAC,EAAE;MAC1B,MAAM,IAAI2D,KAAK,CAAE,oFAAmF3D,IAAK,EAAC,CAAC;IAC7G;IAEA,IAAIA,IAAI,IAAIA,IAAI,CAAC6D,MAAM,GAAG,EAAE,EAAE;MAC5B;MACAC,OAAO,CAACC,IAAI,CAAE,kBAAiB/D,IAAK,2CAA0C,CAAC;MAC/EA,IAAI,GAAGA,IAAI,CAACgE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;IAC9B;IAEA,IAAI,IAAI,CAACpE,SAAS,CAACqE,WAAW,CAACC,IAAI,CAACC,EAAE,IAAIA,EAAE,IAAIA,EAAE,CAACnE,IAAI,CAACoE,WAAW,CAAC,CAAC,KAAKpE,IAAI,CAACoE,WAAW,CAAC,CAAC,CAAC,EAAE;MAC7F,MAAM,IAAIT,KAAK,CAAE,kCAAiC3D,IAAK,EAAC,CAAC;IAC3D;IAEA,IAAI,CAAC0D,KAAK,GAAG1D,IAAI;EACnB;EAEA,IAAIH,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAACD,SAAS;EACvB;;EAEA;EACAyE,OAAOA,CAAA,EAAG;IACR,IAAI,CAACzE,SAAS,CAAC0E,iBAAiB,CAAC,IAAI,CAAC;EACxC;;EAEA;EACA,IAAIC,UAAUA,CAAA,EAAG;IACf,MAAMA,UAAU,GAAG,IAAIvF,KAAK,CAAC,CAAC;IAC9B,IAAI,CAACkB,KAAK,CAACsE,OAAO,CAACC,GAAG,IAAI;MACxB,IAAIA,GAAG,EAAE;QACP,MAAMC,OAAO,GAAGD,GAAG,CAACF,UAAU;QAC9B,IAAIG,OAAO,EAAE;UACXH,UAAU,CAACI,MAAM,CAACF,GAAG,CAACG,MAAM,EAAEF,OAAO,CAACG,GAAG,EAAEJ,GAAG,CAACG,MAAM,EAAEF,OAAO,CAACI,GAAG,CAAC;QACrE;MACF;IACF,CAAC,CAAC;IACF,OAAOP,UAAU;EACnB;;EAEA;EACA;;EAEA;EACA,IAAIQ,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC5E,QAAQ;EACtB;;EAEA;EACA;EACA,IAAI4E,OAAOA,CAACC,KAAK,EAAE;IACjB;IACA,IAAI,CAACC,eAAe,GAAGD,KAAK,CAACE,MAAM,CAAC,CAACC,EAAE,EAAEC,EAAE,KAAK;MAC9C,MAAMC,WAAW,GAAID,EAAE,CAAChE,MAAM,IAAI,CAAC,IAAMgE,EAAE,CAACE,OAAO,IAAIF,EAAE,CAACE,OAAO,CAACzB,MAAO,IAAI,CAAC;MAC9E,OAAO0B,IAAI,CAACT,GAAG,CAACK,EAAE,EAAEE,WAAW,CAAC;IAClC,CAAC,EAAE,CAAC,CAAC;;IAEL;IACA,IAAIG,KAAK,GAAG,CAAC;IACb,MAAMT,OAAO,GAAI,IAAI,CAAC5E,QAAQ,GAAG,EAAG;IACpC6E,KAAK,CAACR,OAAO,CAACiB,IAAI,IAAI;MACpB,MAAMC,MAAM,GAAG,IAAIxG,MAAM,CAAC,IAAI,EAAEsG,KAAK,EAAE,EAAE,KAAK,CAAC;MAC/CT,OAAO,CAACY,IAAI,CAACD,MAAM,CAAC;MACpBA,MAAM,CAACD,IAAI,GAAGA,IAAI;IACpB,CAAC,CAAC;EACJ;EAEAG,YAAYA,CAACC,GAAG,EAAE;IAChB,OAAO,IAAI,CAACzF,KAAK,CAACyF,GAAG,CAAC;EACxB;EAEAC,YAAYA,CAACD,GAAG,EAAEb,KAAK,EAAE;IACvB,IAAI,CAAC5E,KAAK,CAACyF,GAAG,CAAC,GAAGb,KAAK;EACzB;EAEAe,eAAeA,CAACF,GAAG,EAAE;IACnB,OAAO,IAAI,CAACzF,KAAK,CAACyF,GAAG,CAAC;EACxB;EAEAG,aAAaA,CAACC,CAAC,EAAE;IACfpH,CAAC,CAACqH,IAAI,CAAC,IAAI,CAAC9F,KAAK,EAAE6F,CAAC,CAAC;EACvB;;EAEA;EACAE,SAASA,CAACC,CAAC,EAAE;IACX,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MACzB;MACA,MAAMC,GAAG,GAAG,IAAI,CAACjG,KAAK,CAACgG,CAAC,CAAC;MACzB,IAAIC,GAAG,EAAE,OAAOA,GAAG;;MAEnB;MACAD,CAAC,GAAGrH,QAAQ,CAACuH,GAAG,CAACF,CAAC,CAAC;IACrB;IACA,IAAI,CAAC,IAAI,CAACjG,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,EAAE;IACpB;IACA,IAAIiG,CAAC,GAAG,IAAI,CAACjG,QAAQ,CAAC0D,MAAM,EAAE;MAC5B,IAAI0C,CAAC,GAAG,IAAI,CAACpG,QAAQ,CAAC0D,MAAM,GAAG,CAAC;MAChC,OAAO0C,CAAC,IAAIH,CAAC,EAAE;QACb,IAAI,CAACjG,QAAQ,CAACwF,IAAI,CAAC,IAAIzG,MAAM,CAAC,IAAI,EAAEqH,CAAC,EAAE,CAAC,CAAC;MAC3C;IACF;IACA,OAAO,IAAI,CAACpG,QAAQ,CAACiG,CAAC,GAAG,CAAC,CAAC;EAC7B;EAEAI,aAAaA,CAACC,KAAK,EAAEjB,KAAK,EAAc;IACtC,MAAMkB,IAAI,GAAG,IAAI,CAACxG,KAAK;IACvB,MAAMyG,KAAK,GAAGD,IAAI,CAAC7C,MAAM;IAAC,SAAA+C,IAAA,GAAAC,SAAA,CAAAhD,MAAA,EAFGiD,OAAO,OAAAC,KAAA,CAAAH,IAAA,OAAAA,IAAA,WAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;MAAPF,OAAO,CAAAE,IAAA,QAAAH,SAAA,CAAAG,IAAA;IAAA;IAGpC,IAAIF,OAAO,CAACjD,MAAM,GAAG,CAAC,EAAE;MACtB;MACA,KAAK,IAAIoD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,EAAEM,CAAC,EAAE,EAAE;QAC9B,MAAMC,YAAY,GAAG,CAACT,KAAK,EAAEjB,KAAK,CAAC;QACnC;QACAsB,OAAO,CAACtC,OAAO,CAAC2C,MAAM,IAAI;UACxBD,YAAY,CAACvB,IAAI,CAACwB,MAAM,CAACF,CAAC,CAAC,IAAI,IAAI,CAAC;QACtC,CAAC,CAAC;QACF,MAAMxC,GAAG,GAAG,IAAI,CAAC2C,MAAM,CAACH,CAAC,GAAG,CAAC,CAAC;QAC9B;QACAxC,GAAG,CAAC4C,MAAM,CAACC,KAAK,CAAC7C,GAAG,EAAEyC,YAAY,CAAC;MACrC;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAAChH,KAAK,CAACsE,OAAO,CAAC+C,CAAC,IAAI;QACtB,IAAIA,CAAC,EAAE;UACLA,CAAC,CAACF,MAAM,CAACZ,KAAK,EAAEjB,KAAK,CAAC;QACxB;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMgC,OAAO,GAAGV,OAAO,CAACjD,MAAM,GAAG2B,KAAK;IACtC,MAAMiC,KAAK,GAAGhB,KAAK,GAAGjB,KAAK;IAC3B,MAAMkC,IAAI,GAAG,IAAI,CAACvH,QAAQ,CAAC0D,MAAM;IACjC,IAAI2D,OAAO,GAAG,CAAC,EAAE;MACf,KAAK,IAAIP,CAAC,GAAGR,KAAK,GAAGK,OAAO,CAACjD,MAAM,EAAEoD,CAAC,IAAIS,IAAI,EAAET,CAAC,EAAE,EAAE;QACnD,IAAI,CAACd,SAAS,CAACc,CAAC,CAAC,CAACxB,IAAI,GAAG,IAAI,CAACU,SAAS,CAACc,CAAC,GAAGO,OAAO,CAAC,CAAC/B,IAAI;MAC3D;IACF,CAAC,MAAM,IAAI+B,OAAO,GAAG,CAAC,EAAE;MACtB,KAAK,IAAIP,CAAC,GAAGS,IAAI,EAAET,CAAC,IAAIQ,KAAK,EAAER,CAAC,EAAE,EAAE;QAClC,IAAI,CAACd,SAAS,CAACc,CAAC,GAAGO,OAAO,CAAC,CAAC/B,IAAI,GAAG,IAAI,CAACU,SAAS,CAACc,CAAC,CAAC,CAACxB,IAAI;MAC3D;IACF;IACA,KAAK,IAAIwB,CAAC,GAAGR,KAAK,EAAEQ,CAAC,GAAGR,KAAK,GAAGK,OAAO,CAACjD,MAAM,EAAEoD,CAAC,EAAE,EAAE;MACnD,IAAI,CAACd,SAAS,CAACc,CAAC,CAAC,CAACxB,IAAI,GAAG,IAAI;IAC/B;;IAEA;IACA,IAAI,CAAC5F,QAAQ,CAAC8H,YAAY,CAACnB,aAAa,CAAC,IAAI,CAACxG,IAAI,EAAEyG,KAAK,EAAEjB,KAAK,EAAEsB,OAAO,CAACjD,MAAM,CAAC;EACnF;EAEA,IAAI+D,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAACzB,SAAS,CAAC,IAAI,CAAC0B,WAAW,CAAC;EACzC;EAEA,IAAIA,WAAWA,CAAA,EAAG;IAChB,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAI,CAACC,OAAO,CAACtD,GAAG,IAAI;MAClBqD,QAAQ,GAAGvC,IAAI,CAACT,GAAG,CAACgD,QAAQ,EAAErD,GAAG,CAACuD,SAAS,CAAC;IAC9C,CAAC,CAAC;IACF,OAAOF,QAAQ;EACjB;EAEA,IAAIG,iBAAiBA,CAAA,EAAG;IACtB;IACA,MAAMC,MAAM,GAAG,EAAE;IACjB,IAAI1C,KAAK,GAAG,CAAC;IACb,IAAI,CAACuC,OAAO,CAACtD,GAAG,IAAI;MAClBA,GAAG,CAAC0D,QAAQ,CAACC,IAAA,IAAW;QAAA,IAAV;UAAC/B;QAAG,CAAC,GAAA+B,IAAA;QACjB,IAAI,CAACF,MAAM,CAAC7B,GAAG,CAAC,EAAE;UAChB6B,MAAM,CAAC7B,GAAG,CAAC,GAAG,IAAI;UAClBb,KAAK,EAAE;QACT;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOA,KAAK;EACd;;EAEA;EACA;;EAEA6C,UAAUA,CAAA,EAAG;IACX;EAAA;EAGF,IAAIC,cAAcA,CAAA,EAAG;IACnB;IACA,MAAM5B,IAAI,GAAG,IAAI,CAACxG,KAAK;IACvB,IAAIqG,CAAC,GAAGG,IAAI,CAAC7C,MAAM;IACnB,OAAO0C,CAAC,GAAG,CAAC,IAAIG,IAAI,CAACH,CAAC,GAAG,CAAC,CAAC,KAAKpE,SAAS,EAAE;MACzCoE,CAAC,EAAE;IACL;IACA,OAAOA,CAAC;EACV;EAEA,IAAIgC,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAACD,cAAc,GAAG,CAAC;EAChC;EAEA,IAAIE,OAAOA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACtI,KAAK,CAAC2D,MAAM,EAAE;MACrB,OAAO,IAAI,CAAC3D,KAAK,CAAC,IAAI,CAACA,KAAK,CAAC2D,MAAM,GAAG,CAAC,CAAC;IAC1C;IACA,OAAO1B,SAAS;EAClB;;EAEA;EACAsG,OAAOA,CAAClB,CAAC,EAAE;IACT,OAAO,IAAI,CAACrH,KAAK,CAACqH,CAAC,GAAG,CAAC,CAAC;EAC1B;;EAEA;EACAmB,QAAQA,CAACjC,KAAK,EAAE5C,MAAM,EAAE;IACtB,OAAO,IAAI,CAAC3D,KAAK,CAACyI,KAAK,CAAClC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC,GAAG5C,MAAM,CAAC;EACxD;EAEA,IAAI+E,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAACN,cAAc;EAC5B;EAEA,IAAIO,cAAcA,CAAA,EAAG;IACnB;IACA,IAAIrD,KAAK,GAAG,CAAC;IACb,IAAI,CAACuC,OAAO,CAAC,MAAM;MACjBvC,KAAK,EAAE;IACT,CAAC,CAAC;IACF,OAAOA,KAAK;EACd;;EAEA;EACA4B,MAAMA,CAACG,CAAC,EAAE;IACR,IAAI9C,GAAG,GAAG,IAAI,CAACvE,KAAK,CAACqH,CAAC,GAAG,CAAC,CAAC;IAC3B,IAAI,CAAC9C,GAAG,EAAE;MACRA,GAAG,GAAG,IAAI,CAACvE,KAAK,CAACqH,CAAC,GAAG,CAAC,CAAC,GAAG,IAAItI,GAAG,CAAC,IAAI,EAAEsI,CAAC,CAAC;IAC5C;IACA,OAAO9C,GAAG;EACZ;;EAEA;EACAqE,OAAOA,CAACrC,KAAK,EAAE5C,MAAM,EAAE;IACrB,IAAIA,MAAM,GAAG,CAAC,EAAE,OAAO1B,SAAS;IAChC,MAAMuE,IAAI,GAAG,EAAE;IACf,KAAK,IAAIO,CAAC,GAAGR,KAAK,EAAEQ,CAAC,GAAGR,KAAK,GAAG5C,MAAM,EAAEoD,CAAC,EAAE,EAAE;MAC3CP,IAAI,CAACf,IAAI,CAAC,IAAI,CAACyB,MAAM,CAACH,CAAC,CAAC,CAAC;IAC3B;IACA,OAAOP,IAAI;EACb;EAEAqC,MAAMA,CAAC/D,KAAK,EAAe;IAAA,IAAbgE,KAAK,GAAAnC,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAA1E,SAAA,GAAA0E,SAAA,MAAG,GAAG;IACvB,MAAMoC,KAAK,GAAG,IAAI,CAACV,QAAQ;IAC3B,MAAM9D,GAAG,GAAG,IAAI,CAAC2C,MAAM,CAAC6B,KAAK,CAAC;IAC9BxE,GAAG,CAACyE,MAAM,GAAGlE,KAAK;IAClB,IAAI,CAACmE,eAAe,CAACF,KAAK,EAAED,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGA,KAAK,GAAG,GAAG,CAAC;IAC3D,OAAOvE,GAAG;EACZ;EAEA2E,OAAOA,CAACpE,KAAK,EAAe;IAAA,IAAbgE,KAAK,GAAAnC,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAA1E,SAAA,GAAA0E,SAAA,MAAG,GAAG;IACxB,MAAMH,IAAI,GAAG,EAAE;IACf1B,KAAK,CAACR,OAAO,CAACC,GAAG,IAAI;MACnBiC,IAAI,CAACf,IAAI,CAAC,IAAI,CAACoD,MAAM,CAACtE,GAAG,EAAEuE,KAAK,CAAC,CAAC;IACpC,CAAC,CAAC;IACF,OAAOtC,IAAI;EACb;EAEA2C,SAASA,CAACC,GAAG,EAAEtE,KAAK,EAAe;IAAA,IAAbgE,KAAK,GAAAnC,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAA1E,SAAA,GAAA0E,SAAA,MAAG,GAAG;IAC/B,IAAI,CAAC0C,UAAU,CAACD,GAAG,EAAE,CAAC,EAAEtE,KAAK,CAAC;IAC9B,IAAI,CAACmE,eAAe,CAACG,GAAG,EAAEN,KAAK,CAAC;IAChC,OAAO,IAAI,CAAC5B,MAAM,CAACkC,GAAG,CAAC;EACzB;EAEAE,UAAUA,CAACF,GAAG,EAAEJ,MAAM,EAAe;IAAA,IAAbF,KAAK,GAAAnC,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAA1E,SAAA,GAAA0E,SAAA,MAAG,GAAG;IACjC,IAAI,CAAC0C,UAAU,CAACD,GAAG,EAAE,CAAC,EAAE,GAAGJ,MAAM,CAAC;IAClC,IAAIF,KAAK,KAAK,GAAG,EAAE;MACjB;MACA,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,MAAM,CAACrF,MAAM,EAAEoD,CAAC,EAAE,EAAE;QACtC,IAAI+B,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAACP,OAAO,CAACS,MAAM,CAACrF,MAAM,GAAGyF,GAAG,GAAGrC,CAAC,CAAC,KAAK9E,SAAS,EAAE;UAC3E,IAAI,CAACsH,UAAU,CAACP,MAAM,CAACrF,MAAM,GAAGyF,GAAG,GAAGrC,CAAC,EAAEqC,GAAG,GAAGrC,CAAC,EAAE+B,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;QACrE,CAAC,MAAM,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAACP,OAAO,CAACa,GAAG,GAAG,CAAC,CAAC,KAAKnH,SAAS,EAAE;UAClE,IAAI,CAACsH,UAAU,CAACH,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGrC,CAAC,EAAE+B,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;QACrD;MACF;IACF;IACA,OAAO,IAAI,CAACF,OAAO,CAACQ,GAAG,EAAEJ,MAAM,CAACrF,MAAM,CAAC;EACzC;;EAEA;EACAsF,eAAeA,CAACG,GAAG,EAAe;IAAA,IAAbN,KAAK,GAAAnC,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAA1E,SAAA,GAAA0E,SAAA,MAAG,GAAG;IAC9B,IAAImC,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAACP,OAAO,CAACa,GAAG,GAAG,CAAC,CAAC,KAAKnH,SAAS,EAAE;MAC3D,IAAI,CAACsH,UAAU,CAACH,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAEN,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;IACjD,CAAC,MAAM,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAACP,OAAO,CAACa,GAAG,GAAG,CAAC,CAAC,KAAKnH,SAAS,EAAE;MAClE,IAAI,CAACsH,UAAU,CAACH,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAEN,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;IACjD;EACF;EAEAS,UAAUA,CAACC,GAAG,EAAEC,IAAI,EAAsB;IAAA,IAApBC,UAAU,GAAA/C,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAA1E,SAAA,GAAA0E,SAAA,MAAG,KAAK;IACtC,MAAMgD,IAAI,GAAG,IAAI,CAACzC,MAAM,CAACsC,GAAG,CAAC;IAC7B,MAAMI,IAAI,GAAG,IAAI,CAAC1C,MAAM,CAACuC,IAAI,CAAC;IAC9BG,IAAI,CAACd,KAAK,GAAGxJ,SAAS,CAACqK,IAAI,CAACb,KAAK,CAAC;IAClC;IACAa,IAAI,CAAC1B,QAAQ,CAAC;MAAC4B,YAAY,EAAEH;IAAU,CAAC,EAAE,CAACI,IAAI,EAAEC,SAAS,KAAK;MAC7DH,IAAI,CAACI,OAAO,CAACD,SAAS,CAAC,CAACjB,KAAK,GAAGxJ,SAAS,CAACwK,IAAI,CAAChB,KAAK,CAAC;IACvD,CAAC,CAAC;IACFc,IAAI,CAACK,MAAM,GAAGN,IAAI,CAACM,MAAM;EAC3B;EAEAC,YAAYA,CAACC,MAAM,EAAE7E,KAAK,EAAkB;IAAA,IAAhB2B,MAAM,GAAAN,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAA1E,SAAA,GAAA0E,SAAA,MAAG,KAAK;IACxC;IACA;;IAEA,MAAMgD,IAAI,GAAG,IAAI,CAAC3J,KAAK,CAACmK,MAAM,GAAG,CAAC,CAAC;IACnC,MAAMvD,OAAO,GAAG,IAAIC,KAAK,CAACvB,KAAK,CAAC,CAAC8E,IAAI,CAACT,IAAI,CAACX,MAAM,CAAC;IAClD,IAAI,CAACK,UAAU,CAACc,MAAM,GAAG,CAAC,EAAElD,MAAM,GAAG,CAAC,GAAG3B,KAAK,EAAE,GAAGsB,OAAO,CAAC;;IAE3D;IACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzB,KAAK,EAAEyB,CAAC,EAAE,EAAE;MAC9B,MAAM6C,IAAI,GAAG,IAAI,CAAC5J,KAAK,CAACmK,MAAM,GAAGpD,CAAC,CAAC;MACnC6C,IAAI,CAACd,KAAK,GAAGa,IAAI,CAACb,KAAK;MACvBc,IAAI,CAACK,MAAM,GAAGN,IAAI,CAACM,MAAM;MACzB;MACAN,IAAI,CAAC1B,QAAQ,CAAC;QAAC4B,YAAY,EAAE;MAAI,CAAC,EAAE,CAACC,IAAI,EAAEC,SAAS,KAAK;QACvDH,IAAI,CAACI,OAAO,CAACD,SAAS,CAAC,CAACjB,KAAK,GAAGgB,IAAI,CAAChB,KAAK;MAC5C,CAAC,CAAC;IACJ;EACF;EAEAO,UAAUA,CAAC9C,KAAK,EAAEjB,KAAK,EAAc;IACnC;IACA,MAAMiC,KAAK,GAAGhB,KAAK,GAAGjB,KAAK;IAAC,SAAA+E,KAAA,GAAA1D,SAAA,CAAAhD,MAAA,EAFFiD,OAAO,OAAAC,KAAA,CAAAwD,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAP1D,OAAO,CAAA0D,KAAA,QAAA3D,SAAA,CAAA2D,KAAA;IAAA;IAGjC,MAAMC,QAAQ,GAAG3D,OAAO,CAACjD,MAAM;IAC/B,MAAM2D,OAAO,GAAGiD,QAAQ,GAAGjF,KAAK;IAChC,MAAMkC,IAAI,GAAG,IAAI,CAACxH,KAAK,CAAC2D,MAAM;IAC9B,IAAIoD,CAAC;IACL,IAAI4C,IAAI;IACR,IAAIrC,OAAO,GAAG,CAAC,EAAE;MACf;MACA,IAAIf,KAAK,KAAKiB,IAAI,EAAE;QAClB,IAAI,CAACxH,KAAK,CAACwH,IAAI,GAAG,CAAC,CAAC,GAAGvF,SAAS;MAClC;MACA,KAAK8E,CAAC,GAAGQ,KAAK,EAAER,CAAC,IAAIS,IAAI,EAAET,CAAC,EAAE,EAAE;QAC9B4C,IAAI,GAAG,IAAI,CAAC3J,KAAK,CAAC+G,CAAC,GAAG,CAAC,CAAC;QACxB,IAAI4C,IAAI,EAAE;UACR,MAAMC,IAAI,GAAG,IAAI,CAAC1C,MAAM,CAACH,CAAC,GAAGO,OAAO,CAAC;UACrCsC,IAAI,CAACZ,MAAM,GAAGW,IAAI,CAACX,MAAM;UACzBY,IAAI,CAACd,KAAK,GAAGa,IAAI,CAACb,KAAK;UACvBc,IAAI,CAACK,MAAM,GAAGN,IAAI,CAACM,MAAM;UACzB;UACAN,IAAI,CAAC1B,QAAQ,CAAC;YAAC4B,YAAY,EAAE;UAAI,CAAC,EAAE,CAACC,IAAI,EAAEC,SAAS,KAAK;YACvDH,IAAI,CAACI,OAAO,CAACD,SAAS,CAAC,CAACjB,KAAK,GAAGgB,IAAI,CAAChB,KAAK;UAC5C,CAAC,CAAC;UACF,IAAI,CAAC9I,KAAK,CAAC+G,CAAC,GAAG,CAAC,CAAC,GAAG9E,SAAS;QAC/B,CAAC,MAAM;UACL,IAAI,CAACjC,KAAK,CAAC+G,CAAC,GAAGO,OAAO,GAAG,CAAC,CAAC,GAAGrF,SAAS;QACzC;MACF;IACF,CAAC,MAAM,IAAIqF,OAAO,GAAG,CAAC,EAAE;MACtB;MACA,KAAKP,CAAC,GAAGS,IAAI,EAAET,CAAC,IAAIQ,KAAK,EAAER,CAAC,EAAE,EAAE;QAC9B4C,IAAI,GAAG,IAAI,CAAC3J,KAAK,CAAC+G,CAAC,GAAG,CAAC,CAAC;QACxB,IAAI4C,IAAI,EAAE;UACR,MAAMC,IAAI,GAAG,IAAI,CAAC1C,MAAM,CAACH,CAAC,GAAGO,OAAO,CAAC;UACrCsC,IAAI,CAACZ,MAAM,GAAGW,IAAI,CAACX,MAAM;UACzBY,IAAI,CAACd,KAAK,GAAGa,IAAI,CAACb,KAAK;UACvBc,IAAI,CAACK,MAAM,GAAGN,IAAI,CAACM,MAAM;UACzB;UACAN,IAAI,CAAC1B,QAAQ,CAAC;YAAC4B,YAAY,EAAE;UAAI,CAAC,EAAE,CAACC,IAAI,EAAEC,SAAS,KAAK;YACvDH,IAAI,CAACI,OAAO,CAACD,SAAS,CAAC,CAACjB,KAAK,GAAGgB,IAAI,CAAChB,KAAK;;YAE1C;YACA,IAAIgB,IAAI,CAACU,MAAM,CAAChL,WAAW,CAACM,IAAI,KAAK,YAAY,EAAE;cACjD,MAAM2K,cAAc,GAAG,IAAI,CAACvD,MAAM,CAAC4C,IAAI,CAACY,IAAI,CAACC,OAAO,GAAGJ,QAAQ,CAAC,CAACP,OAAO,CAACD,SAAS,CAAC;cACnF,MAAMa,UAAU,GAAGd,IAAI,CAACU,MAAM,CAACK,OAAO;cACtC,MAAMC,SAAS,GAAG,IAAI,CAAC5D,MAAM,CAAC0D,UAAU,CAACF,IAAI,CAACC,OAAO,GAAGJ,QAAQ,CAAC,CAACP,OAAO,CAACY,UAAU,CAACG,OAAO,CAACJ,OAAO,CAAC;cACrGF,cAAc,CAACO,KAAK,CAACF,SAAS,CAAC;YACjC;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAAC9K,KAAK,CAAC+G,CAAC,GAAGO,OAAO,GAAG,CAAC,CAAC,GAAGrF,SAAS;QACzC;MACF;IACF;;IAEA;IACA,KAAK8E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwD,QAAQ,EAAExD,CAAC,EAAE,EAAE;MAC7B,MAAM6C,IAAI,GAAG,IAAI,CAAC1C,MAAM,CAACX,KAAK,GAAGQ,CAAC,CAAC;MACnC6C,IAAI,CAACd,KAAK,GAAG,CAAC,CAAC;MACfc,IAAI,CAACZ,MAAM,GAAGpC,OAAO,CAACG,CAAC,CAAC;IAC1B;;IAEA;IACA,IAAI,CAACpH,QAAQ,CAAC8H,YAAY,CAAC4B,UAAU,CAAC,IAAI,CAACvJ,IAAI,EAAEyG,KAAK,EAAEjB,KAAK,EAAEiF,QAAQ,CAAC;EAC1E;;EAEA;EACA1C,OAAOA,CAACpI,OAAO,EAAEwL,QAAQ,EAAE;IACzB,IAAI,CAACA,QAAQ,EAAE;MACbA,QAAQ,GAAGxL,OAAO;MAClBA,OAAO,GAAGwC,SAAS;IACrB;IACA,IAAIxC,OAAO,IAAIA,OAAO,CAACoK,YAAY,EAAE;MACnC,MAAMxD,CAAC,GAAG,IAAI,CAACrG,KAAK,CAAC2D,MAAM;MAC3B,KAAK,IAAIoD,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIV,CAAC,EAAEU,CAAC,EAAE,EAAE;QAC3BkE,QAAQ,CAAC,IAAI,CAAC/D,MAAM,CAACH,CAAC,CAAC,EAAEA,CAAC,CAAC;MAC7B;IACF,CAAC,MAAM;MACL,IAAI,CAAC/G,KAAK,CAACsE,OAAO,CAACC,GAAG,IAAI;QACxB,IAAIA,GAAG,IAAIA,GAAG,CAAC2G,SAAS,EAAE;UACxBD,QAAQ,CAAC1G,GAAG,EAAEA,GAAG,CAACG,MAAM,CAAC;QAC3B;MACF,CAAC,CAAC;IACJ;EACF;;EAEA;EACAyG,cAAcA,CAAA,EAAG;IACf,MAAM3E,IAAI,GAAG,EAAE;IACf,IAAI,CAACxG,KAAK,CAACsE,OAAO,CAACC,GAAG,IAAI;MACxB,IAAIA,GAAG,EAAE;QACPiC,IAAI,CAACjC,GAAG,CAACG,MAAM,CAAC,GAAGH,GAAG,CAACyE,MAAM;MAC/B;IACF,CAAC,CAAC;IACF,OAAOxC,IAAI;EACb;;EAEA;EACA;;EAEA;EACA4E,QAAQA,CAAC/D,CAAC,EAAEnB,CAAC,EAAE;IACb,MAAMmF,OAAO,GAAGxM,QAAQ,CAACyM,UAAU,CAACjE,CAAC,EAAEnB,CAAC,CAAC;IACzC,MAAM3B,GAAG,GAAG,IAAI,CAACvE,KAAK,CAACqL,OAAO,CAAC9G,GAAG,GAAG,CAAC,CAAC;IACvC,OAAOA,GAAG,GAAGA,GAAG,CAAC6G,QAAQ,CAACC,OAAO,CAAClF,GAAG,CAAC,GAAGlE,SAAS;EACpD;;EAEA;EACA+H,OAAOA,CAAC3C,CAAC,EAAEnB,CAAC,EAAE;IACZ,MAAMmF,OAAO,GAAGxM,QAAQ,CAACyM,UAAU,CAACjE,CAAC,EAAEnB,CAAC,CAAC;IACzC,MAAM3B,GAAG,GAAG,IAAI,CAAC2C,MAAM,CAACmE,OAAO,CAAC9G,GAAG,CAAC;IACpC,OAAOA,GAAG,CAACgH,SAAS,CAACF,OAAO,CAAC;EAC/B;;EAEA;EACA;;EAEA;EACAG,UAAUA,CAAA,EAAW;IAAA,SAAAC,KAAA,GAAA9E,SAAA,CAAAhD,MAAA,EAAP+H,KAAK,OAAA7E,KAAA,CAAA4E,KAAA,GAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;MAALD,KAAK,CAAAC,KAAA,IAAAhF,SAAA,CAAAgF,KAAA;IAAA;IACjB,MAAMtH,UAAU,GAAG,IAAIvF,KAAK,CAAC4M,KAAK,CAAC;IACnC,IAAI,CAACE,mBAAmB,CAACvH,UAAU,CAAC;EACtC;EAEAwH,sBAAsBA,CAAA,EAAW;IAAA,SAAAC,KAAA,GAAAnF,SAAA,CAAAhD,MAAA,EAAP+H,KAAK,OAAA7E,KAAA,CAAAiF,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAALL,KAAK,CAAAK,KAAA,IAAApF,SAAA,CAAAoF,KAAA;IAAA;IAC7B,MAAM1H,UAAU,GAAG,IAAIvF,KAAK,CAAC4M,KAAK,CAAC;IACnC,IAAI,CAACE,mBAAmB,CAACvH,UAAU,EAAE,IAAI,CAAC;EAC5C;EAEAuH,mBAAmBA,CAACvH,UAAU,EAAE2H,WAAW,EAAE;IAC3C;IACArN,CAAC,CAACqH,IAAI,CAAC,IAAI,CAAC7F,OAAO,EAAE6K,KAAK,IAAI;MAC5B,IAAIA,KAAK,CAACiB,UAAU,CAAC5H,UAAU,CAAC,EAAE;QAChC,MAAM,IAAIZ,KAAK,CAAC,mCAAmC,CAAC;MACtD;IACF,CAAC,CAAC;;IAEF;IACA,MAAMyI,MAAM,GAAG,IAAI,CAAClC,OAAO,CAAC3F,UAAU,CAACrD,GAAG,EAAEqD,UAAU,CAACvD,IAAI,CAAC;IAC5D,KAAK,IAAIiG,CAAC,GAAG1C,UAAU,CAACrD,GAAG,EAAE+F,CAAC,IAAI1C,UAAU,CAACpD,MAAM,EAAE8F,CAAC,EAAE,EAAE;MACxD,KAAK,IAAIoF,CAAC,GAAG9H,UAAU,CAACvD,IAAI,EAAEqL,CAAC,IAAI9H,UAAU,CAACtD,KAAK,EAAEoL,CAAC,EAAE,EAAE;QACxD;QACA,IAAIpF,CAAC,GAAG1C,UAAU,CAACrD,GAAG,IAAImL,CAAC,GAAG9H,UAAU,CAACvD,IAAI,EAAE;UAC7C,IAAI,CAACkJ,OAAO,CAACjD,CAAC,EAAEoF,CAAC,CAAC,CAACnB,KAAK,CAACkB,MAAM,EAAEF,WAAW,CAAC;QAC/C;MACF;IACF;;IAEA;IACA,IAAI,CAAC7L,OAAO,CAAC+L,MAAM,CAACb,OAAO,CAAC,GAAGhH,UAAU;EAC3C;EAEA+H,cAAcA,CAACF,MAAM,EAAE;IACrB;IACA,MAAMlB,KAAK,GAAG,IAAI,CAAC7K,OAAO,CAAC+L,MAAM,CAACb,OAAO,CAAC;IAC1C,IAAIL,KAAK,EAAE;MACT,KAAK,IAAIjE,CAAC,GAAGiE,KAAK,CAAChK,GAAG,EAAE+F,CAAC,IAAIiE,KAAK,CAAC/J,MAAM,EAAE8F,CAAC,EAAE,EAAE;QAC9C,KAAK,IAAIoF,CAAC,GAAGnB,KAAK,CAAClK,IAAI,EAAEqL,CAAC,IAAInB,KAAK,CAACjK,KAAK,EAAEoL,CAAC,EAAE,EAAE;UAC9C,IAAI,CAACnC,OAAO,CAACjD,CAAC,EAAEoF,CAAC,CAAC,CAACE,OAAO,CAAC,CAAC;QAC9B;MACF;MACA,OAAO,IAAI,CAAClM,OAAO,CAAC+L,MAAM,CAACb,OAAO,CAAC;IACrC;EACF;EAEA,IAAIiB,SAASA,CAAA,EAAG;IACd;IACA,OAAO3N,CAAC,CAAC4N,IAAI,CAAC,IAAI,CAACpM,OAAO,EAAEqM,OAAO,CAAC;EACtC;;EAEA;EACA;EACA;EACAC,YAAYA,CAAA,EAAW;IAAA,SAAAC,KAAA,GAAA/F,SAAA,CAAAhD,MAAA,EAAP+H,KAAK,OAAA7E,KAAA,CAAA6F,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAALjB,KAAK,CAAAiB,KAAA,IAAAhG,SAAA,CAAAgG,KAAA;IAAA;IACnB,MAAMtI,UAAU,GAAG,IAAIvF,KAAK,CAAC4M,KAAK,CAAC;;IAEnC;IACA,KAAK,IAAI3E,CAAC,GAAG1C,UAAU,CAACrD,GAAG,EAAE+F,CAAC,IAAI1C,UAAU,CAACpD,MAAM,EAAE8F,CAAC,EAAE,EAAE;MACxD,KAAK,IAAIoF,CAAC,GAAG9H,UAAU,CAACvD,IAAI,EAAEqL,CAAC,IAAI9H,UAAU,CAACtD,KAAK,EAAEoL,CAAC,EAAE,EAAE;QACxD,MAAMrC,IAAI,GAAG,IAAI,CAACsB,QAAQ,CAACrE,CAAC,EAAEoF,CAAC,CAAC;QAChC,IAAIrC,IAAI,EAAE;UACR,IAAIA,IAAI,CAAC8C,IAAI,KAAK3N,KAAK,CAAC4N,SAAS,CAACC,KAAK,EAAE;YACvC;YACA,IAAI,CAACV,cAAc,CAACtC,IAAI,CAACoC,MAAM,CAAC;UAClC,CAAC,MAAM,IAAI,IAAI,CAAC/L,OAAO,CAAC2J,IAAI,CAACuB,OAAO,CAAC,EAAE;YACrC;YACA,IAAI,CAACe,cAAc,CAACtC,IAAI,CAAC;UAC3B;QACF;MACF;IACF;EACF;;EAEA;EACA;EACAiD,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAwB;IAAA,IAAtBC,SAAS,GAAAxG,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAA1E,SAAA,GAAA0E,SAAA,MAAG,QAAQ;IACvD;IACA,MAAMyG,OAAO,GAAGvO,QAAQ,CAACwO,MAAM,CAACL,KAAK,CAAC;IACtC,MAAM;MAAChM,GAAG;MAAEF,IAAI;MAAEG,MAAM;MAAEF;IAAK,CAAC,GAAGqM,OAAO;IAC1C,MAAME,KAAK,GAAGvM,KAAK,GAAGD,IAAI,GAAG,CAAC;IAC9B,MAAMyM,aAAa,GAAG1O,QAAQ,CAAC2O,aAAa,CAACxM,GAAG,EAAEF,IAAI,CAAC;IACvD,MAAM2M,QAAQ,GAAGN,SAAS,KAAK,QAAQ;;IAEvC;IACA,IAAIO,SAAS;IACb,IAAI,OAAOR,OAAO,KAAK,UAAU,EAAE;MACjCQ,SAAS,GAAGR,OAAO;IACrB,CAAC,MAAM,IAAIrG,KAAK,CAAC8G,OAAO,CAACT,OAAO,CAAC,EAAE;MACjC,IAAIrG,KAAK,CAAC8G,OAAO,CAACT,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7BQ,SAAS,GAAGA,CAACnJ,GAAG,EAAE4B,GAAG,KAAK+G,OAAO,CAAC3I,GAAG,GAAGvD,GAAG,CAAC,CAACmF,GAAG,GAAGrF,IAAI,CAAC;MAC1D,CAAC,MAAM;QACL;QACA4M,SAAS,GAAGA,CAACnJ,GAAG,EAAE4B,GAAG,KAAK+G,OAAO,CAAC,CAAC3I,GAAG,GAAGvD,GAAG,IAAIsM,KAAK,IAAInH,GAAG,GAAGrF,IAAI,CAAC,CAAC;MACvE;IACF,CAAC,MAAM;MACL4M,SAAS,GAAGA,CAAA,KAAMzL,SAAS;IAC7B;IACA,IAAI2L,KAAK,GAAG,IAAI;IAChB,KAAK,IAAIvG,CAAC,GAAGrG,GAAG,EAAEqG,CAAC,IAAIpG,MAAM,EAAEoG,CAAC,EAAE,EAAE;MAClC,KAAK,IAAInB,CAAC,GAAGpF,IAAI,EAAEoF,CAAC,IAAInF,KAAK,EAAEmF,CAAC,EAAE,EAAE;QAClC,IAAI0H,KAAK,EAAE;UACT,IAAI,CAAC5D,OAAO,CAAC3C,CAAC,EAAEnB,CAAC,CAAC,CAACpB,KAAK,GAAG;YACzBqI,SAAS;YACTF,OAAO;YACPY,GAAG,EAAEb,KAAK;YACVc,MAAM,EAAEJ,SAAS,CAACrG,CAAC,EAAEnB,CAAC;UACxB,CAAC;UACD0H,KAAK,GAAG,KAAK;QACf,CAAC,MAAM;UACL,IAAI,CAAC5D,OAAO,CAAC3C,CAAC,EAAEnB,CAAC,CAAC,CAACpB,KAAK,GAAG2I,QAAQ,GAC/B;YACEM,aAAa,EAAER,aAAa;YAC5BO,MAAM,EAAEJ,SAAS,CAACrG,CAAC,EAAEnB,CAAC;UACxB,CAAC,GACDwH,SAAS,CAACrG,CAAC,EAAEnB,CAAC,CAAC;QACrB;MACF;IACF;EACF;;EAEA;EACA;EACA8H,QAAQA,CAACC,OAAO,EAAEjB,KAAK,EAAE;IACvB,MAAMkB,KAAK,GAAG;MACZtB,IAAI,EAAE,OAAO;MACbqB,OAAO;MACPjB;IACF,CAAC;IACD,IAAI,CAAC5J,MAAM,CAACqC,IAAI,CAAC,IAAIvG,KAAK,CAAC,IAAI,EAAEgP,KAAK,CAAC,CAAC;EAC1C;EAEAC,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC/K,MAAM,CAACgL,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,IAAI,KAAK,OAAO,CAAC;EACpD;EAEA0B,kBAAkBA,CAACL,OAAO,EAAE;IAC1B,MAAMC,KAAK,GAAG;MACZtB,IAAI,EAAE,YAAY;MAClBqB;IACF,CAAC;IACD,IAAI,CAAC7K,MAAM,CAACqC,IAAI,CAAC,IAAIvG,KAAK,CAAC,IAAI,EAAEgP,KAAK,CAAC,CAAC;EAC1C;EAEAK,oBAAoBA,CAAA,EAAG;IACrB,MAAMC,KAAK,GAAG,IAAI,CAACpL,MAAM,CAACY,IAAI,CAACqK,CAAC,IAAIA,CAAC,CAACzB,IAAI,KAAK,YAAY,CAAC;IAC5D,OAAO4B,KAAK,IAAIA,KAAK,CAACP,OAAO;EAC/B;;EAEA;EACA;EACAQ,OAAOA,CAACC,QAAQ,EAAEjP,OAAO,EAAE;IACzB;IACA;IACA,OAAO,IAAIkP,OAAO,CAACC,OAAO,IAAI;MAC5B,IAAI,CAACvL,eAAe,GAAG;QACrBwL,KAAK,EAAE;MACT,CAAC;MACD,IAAIpP,OAAO,IAAI,WAAW,IAAIA,OAAO,EAAE;QACrC;QACAA,OAAO,CAACqP,SAAS,GAAGC,MAAM,CAACC,QAAQ,CAACvP,OAAO,CAACqP,SAAS,CAAC,GAAGzJ,IAAI,CAAC4J,KAAK,CAAC5J,IAAI,CAACT,GAAG,CAAC,CAAC,EAAEnF,OAAO,CAACqP,SAAS,CAAC,CAAC,GAAG,MAAM;MAC9G;MACA,IAAIJ,QAAQ,EAAE;QACZ,IAAI,CAACrL,eAAe,CAAC6L,aAAa,GAAG,SAAS;QAC9C,IAAI,CAAC7L,eAAe,CAAC8L,SAAS,GAAG9P,SAAS,CAAC+P,WAAW,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC;QAC7E,IAAI,CAAChM,eAAe,CAACyL,SAAS,GAAGrP,OAAO,IAAI,WAAW,IAAIA,OAAO,GAAGA,OAAO,CAACqP,SAAS,GAAG,MAAM,CAAC,CAAC;QACjG,IAAI,CAACzL,eAAe,CAACiM,SAAS,GAAGjQ,SAAS,CAACkQ,qBAAqB,CAC9Db,QAAQ,EACR,QAAQ,EACR,IAAI,CAACrL,eAAe,CAAC8L,SAAS,EAC9B,IAAI,CAAC9L,eAAe,CAACyL,SACvB,CAAC;MACH;MACA,IAAIrP,OAAO,EAAE;QACX,IAAI,CAAC4D,eAAe,GAAG/C,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC8C,eAAe,EAAE5D,OAAO,CAAC;QACnE,IAAI,CAACiP,QAAQ,IAAI,WAAW,IAAIjP,OAAO,EAAE;UACvC,OAAO,IAAI,CAAC4D,eAAe,CAACyL,SAAS;QACvC;MACF;MACAF,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEAY,SAASA,CAAA,EAAG;IACV,IAAI,CAACnM,eAAe,GAAG,IAAI;EAC7B;;EAEA;EACA;EACAoM,QAAQA,CAACvB,KAAK,EAAE;IACd,MAAMwB,KAAK,GAAG,IAAIvQ,KAAK,CAAC,IAAI,EAAE+O,KAAK,CAAC;IACpC,IAAI,CAAC5K,MAAM,CAAC4K,KAAK,CAACpO,IAAI,CAAC,GAAG4P,KAAK;IAC/B,OAAOA,KAAK;EACd;EAEAC,QAAQA,CAAC7P,IAAI,EAAE;IACb,OAAO,IAAI,CAACwD,MAAM,CAACxD,IAAI,CAAC;EAC1B;EAEA8P,WAAWA,CAAC9P,IAAI,EAAE;IAChB,OAAO,IAAI,CAACwD,MAAM,CAACxD,IAAI,CAAC;EAC1B;EAEA+P,SAASA,CAAA,EAAG;IACV,OAAOvP,MAAM,CAAC0I,MAAM,CAAC,IAAI,CAAC1F,MAAM,CAAC;EACnC;;EAEA;EACA;EACAwM,wBAAwBA,CAACC,EAAE,EAAE;IAC3B,IAAI,CAACxM,sBAAsB,CAACkC,IAAI,CAACsK,EAAE,CAAC;EACtC;EAEAC,2BAA2BA,CAAC5B,MAAM,EAAE;IAClC,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,IAAI,CAAC7K,sBAAsB,CAAC4D,MAAM,CAACiH,MAAM,EAAE,CAAC,CAAC;IAC/C,CAAC,MAAM,IAAIA,MAAM,YAAY6B,QAAQ,EAAE;MACrC,IAAI,CAAC1M,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAAC6K,MAAM,CAACA,MAAM,CAAC;IAC1E,CAAC,MAAM;MACL,IAAI,CAAC7K,sBAAsB,GAAG,EAAE;IAClC;EACF;;EAEA;EACA;EACA,IAAI2M,QAAQA,CAAA,EAAG;IACb;IACAtM,OAAO,CAACuM,KAAK,CAAC,yFAAyF,CAAC;IACxG,OAAO,IAAI,CAAC9P,UAAU,CAAC6P,QAAQ;EACjC;EAEA,IAAIA,QAAQA,CAACpL,KAAK,EAAE;IAClB;IACAlB,OAAO,CAACuM,KAAK,CAAC,yFAAyF,CAAC;IACxG,IAAI,CAAC9P,UAAU,CAAC6P,QAAQ,GAAGpL,KAAK;EAClC;;EAEA;EACA;;EAEA,IAAIoJ,KAAKA,CAAA,EAAG;IACV,MAAMA,KAAK,GAAG;MACZtO,EAAE,EAAE,IAAI,CAACA,EAAE;MACXE,IAAI,EAAE,IAAI,CAACA,IAAI;MACfmD,eAAe,EAAE,IAAI,CAACA,eAAe,CAACiL,KAAK;MAC3C7N,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BN,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBa,SAAS,EAAE,IAAI,CAACA,SAAS;MACzB4B,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BpC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzB8C,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BiN,KAAK,EAAE,IAAI,CAAChN,MAAM,CAACiN,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACpC,KAAK,CAAC;MAC9C7K,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCC,MAAM,EAAEhD,MAAM,CAAC0I,MAAM,CAAC,IAAI,CAAC1F,MAAM,CAAC,CAAC+M,GAAG,CAACX,KAAK,IAAIA,KAAK,CAACxB,KAAK,CAAC;MAC5D3K,sBAAsB,EAAE,IAAI,CAACA;IAC/B,CAAC;;IAED;IACA;IACA2K,KAAK,CAACqC,IAAI,GAAGvR,MAAM,CAACwR,OAAO,CAAC,IAAI,CAAC3L,OAAO,CAAC;;IAEzC;IACA;IACA,MAAM2B,IAAI,GAAI0H,KAAK,CAAC1H,IAAI,GAAG,EAAG;IAC9B,MAAMnC,UAAU,GAAI6J,KAAK,CAAC7J,UAAU,GAAG,IAAIvF,KAAK,CAAC,CAAE;IACnD,IAAI,CAACkB,KAAK,CAACsE,OAAO,CAACC,GAAG,IAAI;MACxB,MAAMkM,QAAQ,GAAGlM,GAAG,IAAIA,GAAG,CAAC2J,KAAK;MACjC,IAAIuC,QAAQ,EAAE;QACZpM,UAAU,CAACI,MAAM,CAACgM,QAAQ,CAAC/L,MAAM,EAAE+L,QAAQ,CAAC9L,GAAG,EAAE8L,QAAQ,CAAC/L,MAAM,EAAE+L,QAAQ,CAAC7L,GAAG,CAAC;QAC/E4B,IAAI,CAACf,IAAI,CAACgL,QAAQ,CAAC;MACrB;IACF,CAAC,CAAC;;IAEF;IACA;IACAvC,KAAK,CAACwC,MAAM,GAAG,EAAE;IACjB/R,CAAC,CAACqH,IAAI,CAAC,IAAI,CAAC7F,OAAO,EAAE6K,KAAK,IAAI;MAC5BkD,KAAK,CAACwC,MAAM,CAACjL,IAAI,CAACuF,KAAK,CAACgC,KAAK,CAAC;IAChC,CAAC,CAAC;IAEF,OAAOkB,KAAK;EACd;EAEAyC,UAAUA,CAACzC,KAAK,EAAE;IAChB,IAAI,CAAClO,KAAK,GAAG,EAAE;IACfkO,KAAK,CAAC1H,IAAI,CAAClC,OAAO,CAACmM,QAAQ,IAAI;MAC7B,MAAMlM,GAAG,GAAG,IAAIxF,GAAG,CAAC,IAAI,EAAE0R,QAAQ,CAAC/L,MAAM,CAAC;MAC1C,IAAI,CAAC1E,KAAK,CAACuE,GAAG,CAACG,MAAM,GAAG,CAAC,CAAC,GAAGH,GAAG;MAChCA,GAAG,CAAC2J,KAAK,GAAGuC,QAAQ;IACtB,CAAC,CAAC;EACJ;EAEAG,gBAAgBA,CAAC1C,KAAK,EAAE;IACtBvP,CAAC,CAACqH,IAAI,CAACkI,KAAK,CAAC1C,UAAU,EAAER,KAAK,IAAI;MAChC;MACA;MACA,IAAI,CAACa,sBAAsB,CAACb,KAAK,CAAC;IACpC,CAAC,CAAC;EACJ;EAEA,IAAIkD,KAAKA,CAACpJ,KAAK,EAAE;IACf,IAAI,CAAChF,IAAI,GAAGgF,KAAK,CAAChF,IAAI;IACtB,IAAI,CAACG,QAAQ,GAAGjB,MAAM,CAAC6R,SAAS,CAAC,IAAI,EAAE/L,KAAK,CAACyL,IAAI,CAAC;IAClD,IAAI,CAACI,UAAU,CAAC7L,KAAK,CAAC;IAEtB,IAAI,CAAC8L,gBAAgB,CAAC9L,KAAK,CAAC;IAC5B,IAAI,CAAC7B,eAAe,GAAG,IAAI7D,eAAe,CAAC0F,KAAK,CAAC7B,eAAe,CAAC;IACjE,IAAI,CAAC5C,UAAU,GAAGyE,KAAK,CAACzE,UAAU;IAClC,IAAI,CAACO,SAAS,GAAGkE,KAAK,CAAClE,SAAS;IAChC,IAAI,CAAC4B,YAAY,GAAGsC,KAAK,CAACtC,YAAY;IACtC,IAAI,CAACU,KAAK,GAAG4B,KAAK,CAAC5B,KAAK;IACxB,IAAI,CAACC,UAAU,GAAG2B,KAAK,CAAC3B,UAAU;IAClC,IAAI,CAACC,MAAM,GAAG0B,KAAK,CAACsL,KAAK,CAACC,GAAG,CAACC,MAAM,IAAI,IAAIpR,KAAK,CAAC,IAAI,EAAEoR,MAAM,CAAC,CAAC;IAChE,IAAI,CAACjN,eAAe,GAAGyB,KAAK,CAACzB,eAAe;IAC5C,IAAI,CAACC,MAAM,GAAGwB,KAAK,CAACxB,MAAM,CAAC0B,MAAM,CAAC,CAAC1B,MAAM,EAAEoM,KAAK,KAAK;MACnD,MAAMoB,CAAC,GAAG,IAAI3R,KAAK,CAAC,CAAC;MACrB2R,CAAC,CAAC5C,KAAK,GAAGwB,KAAK;MACfpM,MAAM,CAACoM,KAAK,CAAC5P,IAAI,CAAC,GAAGgR,CAAC;MACtB,OAAOxN,MAAM;IACf,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,IAAI,CAACC,sBAAsB,GAAGuB,KAAK,CAACvB,sBAAsB;EAC5D;AACF;AAEAwN,MAAM,CAACC,OAAO,GAAGzR,SAAS"}
{"version": 3, "file": "exceljs.bare.js", "names": ["ExcelJS", "Workbook", "require", "Enums", "Object", "keys", "for<PERSON>ach", "key", "module", "exports"], "sources": ["../../lib/exceljs.bare.js"], "sourcesContent": ["// this bundle is built without polyfill leaving apps the freedom to add their own\nconst ExcelJS = {\n  Workbook: require('./doc/workbook'),\n};\n\n// Object.assign mono-fill\nconst Enums = require('./doc/enums');\n\nObject.keys(Enums).forEach(key => {\n  ExcelJS[key] = Enums[key];\n});\n\nmodule.exports = ExcelJS;\n"], "mappings": ";;AAAA;AACA,MAAMA,OAAO,GAAG;EACdC,QAAQ,EAAEC,OAAO,CAAC,gBAAgB;AACpC,CAAC;;AAED;AACA,MAAMC,KAAK,GAAGD,OAAO,CAAC,aAAa,CAAC;AAEpCE,MAAM,CAACC,IAAI,CAACF,KAAK,CAAC,CAACG,OAAO,CAACC,GAAG,IAAI;EAChCP,OAAO,CAACO,GAAG,CAAC,GAAGJ,KAAK,CAACI,GAAG,CAAC;AAC3B,CAAC,CAAC;AAEFC,MAAM,CAACC,OAAO,GAAGT,OAAO"}
{"version": 3, "file": "app-heading-pairs-xform.js", "names": ["BaseXform", "require", "AppHeadingPairsXform", "render", "xmlStream", "model", "openNode", "size", "baseType", "leafNode", "undefined", "closeNode", "length", "parseOpen", "node", "name", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/core/app-heading-pairs-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass AppHeadingPairsXform extends BaseXform {\n  render(xmlStream, model) {\n    xmlStream.openNode('HeadingPairs');\n    xmlStream.openNode('vt:vector', {size: 2, baseType: 'variant'});\n\n    xmlStream.openNode('vt:variant');\n    xmlStream.leafNode('vt:lpstr', undefined, 'Worksheets');\n    xmlStream.closeNode();\n\n    xmlStream.openNode('vt:variant');\n    xmlStream.leafNode('vt:i4', undefined, model.length);\n    xmlStream.closeNode();\n\n    xmlStream.closeNode();\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    // no parsing\n    return node.name === 'HeadingPairs';\n  }\n\n  parseText() {}\n\n  parseClose(name) {\n    return name !== 'HeadingPairs';\n  }\n}\n\nmodule.exports = AppHeadingPairsXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,oBAAoB,SAASF,SAAS,CAAC;EAC3CG,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,cAAc,CAAC;IAClCF,SAAS,CAACE,QAAQ,CAAC,WAAW,EAAE;MAACC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;IAE/DJ,SAAS,CAACE,QAAQ,CAAC,YAAY,CAAC;IAChCF,SAAS,CAACK,QAAQ,CAAC,UAAU,EAAEC,SAAS,EAAE,YAAY,CAAC;IACvDN,SAAS,CAACO,SAAS,CAAC,CAAC;IAErBP,SAAS,CAACE,QAAQ,CAAC,YAAY,CAAC;IAChCF,SAAS,CAACK,QAAQ,CAAC,OAAO,EAAEC,SAAS,EAAEL,KAAK,CAACO,MAAM,CAAC;IACpDR,SAAS,CAACO,SAAS,CAAC,CAAC;IAErBP,SAAS,CAACO,SAAS,CAAC,CAAC;IACrBP,SAAS,CAACO,SAAS,CAAC,CAAC;EACvB;EAEAE,SAASA,CAACC,IAAI,EAAE;IACd;IACA,OAAOA,IAAI,CAACC,IAAI,KAAK,cAAc;EACrC;EAEAC,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAACF,IAAI,EAAE;IACf,OAAOA,IAAI,KAAK,cAAc;EAChC;AACF;AAEAG,MAAM,CAACC,OAAO,GAAGjB,oBAAoB"}
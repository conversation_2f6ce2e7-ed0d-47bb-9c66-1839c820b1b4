{"version": 3, "file": "app-xform.js", "names": ["XmlStream", "require", "BaseXform", "StringXform", "AppHeadingPairsXform", "AppTitleOfPartsXform", "AppXform", "constructor", "map", "Company", "tag", "Manager", "HeadingPairs", "TitleOfParts", "render", "xmlStream", "model", "openXml", "StdDocAttributes", "openNode", "PROPERTY_ATTRIBUTES", "leafNode", "undefined", "worksheets", "company", "manager", "closeNode", "parseOpen", "node", "parser", "name", "parseText", "text", "parseClose", "DateFormat", "dt", "toISOString", "replace", "DateAttrs", "xmlns", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/core/app-xform.js"], "sourcesContent": ["const XmlStream = require('../../../utils/xml-stream');\nconst BaseXform = require('../base-xform');\nconst StringXform = require('../simple/string-xform');\n\nconst AppHeadingPairsXform = require('./app-heading-pairs-xform');\nconst AppTitleOfPartsXform = require('./app-titles-of-parts-xform');\n\nclass AppXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      Company: new StringXform({tag: 'Company'}),\n      Manager: new StringXform({tag: 'Manager'}),\n      HeadingPairs: new AppHeadingPairsXform(),\n      TitleOfParts: new AppTitleOfPartsXform(),\n    };\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openXml(XmlStream.StdDocAttributes);\n\n    xmlStream.openNode('Properties', AppXform.PROPERTY_ATTRIBUTES);\n\n    xmlStream.leafNode('Application', undefined, 'Microsoft Excel');\n    xmlStream.leafNode('DocSecurity', undefined, '0');\n    xmlStream.leafNode('ScaleCrop', undefined, 'false');\n\n    this.map.HeadingPairs.render(xmlStream, model.worksheets);\n    this.map.TitleOfParts.render(xmlStream, model.worksheets);\n    this.map.Company.render(xmlStream, model.company || '');\n    this.map.Manager.render(xmlStream, model.manager);\n\n    xmlStream.leafNode('LinksUpToDate', undefined, 'false');\n    xmlStream.leafNode('SharedDoc', undefined, 'false');\n    xmlStream.leafNode('HyperlinksChanged', undefined, 'false');\n    xmlStream.leafNode('AppVersion', undefined, '16.0300');\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case 'Properties':\n        return true;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n          return true;\n        }\n\n        // there's a lot we don't bother to parse\n        return false;\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case 'Properties':\n        this.model = {\n          worksheets: this.map.TitleOfParts.model,\n          company: this.map.Company.model,\n          manager: this.map.Manager.model,\n        };\n        return false;\n      default:\n        return true;\n    }\n  }\n}\n\nAppXform.DateFormat = function(dt) {\n  return dt.toISOString().replace(/[.]\\d{3,6}/, '');\n};\n\nAppXform.DateAttrs = {'xsi:type': 'dcterms:W3CDTF'};\n\nAppXform.PROPERTY_ATTRIBUTES = {\n  xmlns: 'http://schemas.openxmlformats.org/officeDocument/2006/extended-properties',\n  'xmlns:vt': 'http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes',\n};\n\nmodule.exports = AppXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AACtD,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAME,WAAW,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AAErD,MAAMG,oBAAoB,GAAGH,OAAO,CAAC,2BAA2B,CAAC;AACjE,MAAMI,oBAAoB,GAAGJ,OAAO,CAAC,6BAA6B,CAAC;AAEnE,MAAMK,QAAQ,SAASJ,SAAS,CAAC;EAC/BK,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACTC,OAAO,EAAE,IAAIN,WAAW,CAAC;QAACO,GAAG,EAAE;MAAS,CAAC,CAAC;MAC1CC,OAAO,EAAE,IAAIR,WAAW,CAAC;QAACO,GAAG,EAAE;MAAS,CAAC,CAAC;MAC1CE,YAAY,EAAE,IAAIR,oBAAoB,CAAC,CAAC;MACxCS,YAAY,EAAE,IAAIR,oBAAoB,CAAC;IACzC,CAAC;EACH;EAEAS,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,OAAO,CAACjB,SAAS,CAACkB,gBAAgB,CAAC;IAE7CH,SAAS,CAACI,QAAQ,CAAC,YAAY,EAAEb,QAAQ,CAACc,mBAAmB,CAAC;IAE9DL,SAAS,CAACM,QAAQ,CAAC,aAAa,EAAEC,SAAS,EAAE,iBAAiB,CAAC;IAC/DP,SAAS,CAACM,QAAQ,CAAC,aAAa,EAAEC,SAAS,EAAE,GAAG,CAAC;IACjDP,SAAS,CAACM,QAAQ,CAAC,WAAW,EAAEC,SAAS,EAAE,OAAO,CAAC;IAEnD,IAAI,CAACd,GAAG,CAACI,YAAY,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACO,UAAU,CAAC;IACzD,IAAI,CAACf,GAAG,CAACK,YAAY,CAACC,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACO,UAAU,CAAC;IACzD,IAAI,CAACf,GAAG,CAACC,OAAO,CAACK,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACQ,OAAO,IAAI,EAAE,CAAC;IACvD,IAAI,CAAChB,GAAG,CAACG,OAAO,CAACG,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACS,OAAO,CAAC;IAEjDV,SAAS,CAACM,QAAQ,CAAC,eAAe,EAAEC,SAAS,EAAE,OAAO,CAAC;IACvDP,SAAS,CAACM,QAAQ,CAAC,WAAW,EAAEC,SAAS,EAAE,OAAO,CAAC;IACnDP,SAAS,CAACM,QAAQ,CAAC,mBAAmB,EAAEC,SAAS,EAAE,OAAO,CAAC;IAC3DP,SAAS,CAACM,QAAQ,CAAC,YAAY,EAAEC,SAAS,EAAE,SAAS,CAAC;IAEtDP,SAAS,CAACW,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,YAAY;QACf,OAAO,IAAI;MACb;QACE,IAAI,CAACD,MAAM,GAAG,IAAI,CAACrB,GAAG,CAACoB,IAAI,CAACE,IAAI,CAAC;QACjC,IAAI,IAAI,CAACD,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;UAC3B,OAAO,IAAI;QACb;;QAEA;QACA,OAAO,KAAK;IAChB;EACF;EAEAG,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACH,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACE,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACH,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACI,UAAU,CAACH,IAAI,CAAC,EAAE;QACjC,IAAI,CAACD,MAAM,GAAGP,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQQ,IAAI;MACV,KAAK,YAAY;QACf,IAAI,CAACd,KAAK,GAAG;UACXO,UAAU,EAAE,IAAI,CAACf,GAAG,CAACK,YAAY,CAACG,KAAK;UACvCQ,OAAO,EAAE,IAAI,CAAChB,GAAG,CAACC,OAAO,CAACO,KAAK;UAC/BS,OAAO,EAAE,IAAI,CAACjB,GAAG,CAACG,OAAO,CAACK;QAC5B,CAAC;QACD,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;AACF;AAEAV,QAAQ,CAAC4B,UAAU,GAAG,UAASC,EAAE,EAAE;EACjC,OAAOA,EAAE,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;AACnD,CAAC;AAED/B,QAAQ,CAACgC,SAAS,GAAG;EAAC,UAAU,EAAE;AAAgB,CAAC;AAEnDhC,QAAQ,CAACc,mBAAmB,GAAG;EAC7BmB,KAAK,EAAE,2EAA2E;EAClF,UAAU,EAAE;AACd,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGnC,QAAQ"}
{"version": 3, "file": "sheet-xform.js", "names": ["utils", "require", "BaseXform", "WorksheetXform", "render", "xmlStream", "model", "leafNode", "sheetId", "id", "name", "state", "rId", "parseOpen", "node", "xmlDecode", "attributes", "parseInt", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/book/sheet-xform.js"], "sourcesContent": ["const utils = require('../../../utils/utils');\nconst BaseXform = require('../base-xform');\n\nclass WorksheetXform extends BaseXform {\n  render(xmlStream, model) {\n    xmlStream.leafNode('sheet', {\n      sheetId: model.id,\n      name: model.name,\n      state: model.state,\n      'r:id': model.rId,\n    });\n  }\n\n  parseOpen(node) {\n    if (node.name === 'sheet') {\n      this.model = {\n        name: utils.xmlDecode(node.attributes.name),\n        id: parseInt(node.attributes.sheetId, 10),\n        state: node.attributes.state,\n        rId: node.attributes['r:id'],\n      };\n      return true;\n    }\n    return false;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = WorksheetXform;\n"], "mappings": ";;AAAA,MAAMA,KAAK,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC7C,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAME,cAAc,SAASD,SAAS,CAAC;EACrCE,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,OAAO,EAAE;MAC1BC,OAAO,EAAEF,KAAK,CAACG,EAAE;MACjBC,IAAI,EAAEJ,KAAK,CAACI,IAAI;MAChBC,KAAK,EAAEL,KAAK,CAACK,KAAK;MAClB,MAAM,EAAEL,KAAK,CAACM;IAChB,CAAC,CAAC;EACJ;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACJ,IAAI,KAAK,OAAO,EAAE;MACzB,IAAI,CAACJ,KAAK,GAAG;QACXI,IAAI,EAAEV,KAAK,CAACe,SAAS,CAACD,IAAI,CAACE,UAAU,CAACN,IAAI,CAAC;QAC3CD,EAAE,EAAEQ,QAAQ,CAACH,IAAI,CAACE,UAAU,CAACR,OAAO,EAAE,EAAE,CAAC;QACzCG,KAAK,EAAEG,IAAI,CAACE,UAAU,CAACL,KAAK;QAC5BC,GAAG,EAAEE,IAAI,CAACE,UAAU,CAAC,MAAM;MAC7B,CAAC;MACD,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAE,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGlB,cAAc"}
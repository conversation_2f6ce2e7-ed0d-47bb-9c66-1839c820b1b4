{"version": 3, "file": "static-xform.js", "names": ["BaseXform", "require", "XmlStream", "build", "xmlStream", "model", "openNode", "tag", "$", "c", "for<PERSON>ach", "child", "t", "writeText", "closeNode", "StaticXform", "constructor", "_model", "render", "_xml", "stream", "xml", "writeXml", "parseOpen", "parseText", "parseClose", "name", "module", "exports"], "sources": ["../../../../lib/xlsx/xform/static-xform.js"], "sourcesContent": ["const BaseXform = require('./base-xform');\nconst XmlStream = require('../../utils/xml-stream');\n\n// const model = {\n//   tag: 'name',\n//   $: {attr: 'value'},\n//   c: [\n//     { tag: 'child' }\n//   ],\n//   t: 'some text'\n// };\n\nfunction build(xmlStream, model) {\n  xmlStream.openNode(model.tag, model.$);\n  if (model.c) {\n    model.c.forEach(child => {\n      build(xmlStream, child);\n    });\n  }\n  if (model.t) {\n    xmlStream.writeText(model.t);\n  }\n  xmlStream.closeNode();\n}\n\nclass StaticXform extends BaseXform {\n  constructor(model) {\n    super();\n\n    // This class is an optimisation for static (unimportant and unchanging) xml\n    // It is stateless - apart from its static model and so can be used as a singleton\n    // Being stateless - it will only track entry to and exit from it's root xml tag during parsing and nothing else\n    // Known issues:\n    //    since stateless - parseOpen always returns true. Parent xform must know when to start using this xform\n    //    if the root tag is recursive, the parsing will behave unpredictably\n    this._model = model;\n  }\n\n  render(xmlStream) {\n    if (!this._xml) {\n      const stream = new XmlStream();\n      build(stream, this._model);\n      this._xml = stream.xml;\n    }\n    xmlStream.writeXml(this._xml);\n  }\n\n  parseOpen() {\n    return true;\n  }\n\n  parseText() {}\n\n  parseClose(name) {\n    switch (name) {\n      case this._model.tag:\n        return false;\n      default:\n        return true;\n    }\n  }\n}\n\nmodule.exports = StaticXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;AACzC,MAAMC,SAAS,GAAGD,OAAO,CAAC,wBAAwB,CAAC;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASE,KAAKA,CAACC,SAAS,EAAEC,KAAK,EAAE;EAC/BD,SAAS,CAACE,QAAQ,CAACD,KAAK,CAACE,GAAG,EAAEF,KAAK,CAACG,CAAC,CAAC;EACtC,IAAIH,KAAK,CAACI,CAAC,EAAE;IACXJ,KAAK,CAACI,CAAC,CAACC,OAAO,CAACC,KAAK,IAAI;MACvBR,KAAK,CAACC,SAAS,EAAEO,KAAK,CAAC;IACzB,CAAC,CAAC;EACJ;EACA,IAAIN,KAAK,CAACO,CAAC,EAAE;IACXR,SAAS,CAACS,SAAS,CAACR,KAAK,CAACO,CAAC,CAAC;EAC9B;EACAR,SAAS,CAACU,SAAS,CAAC,CAAC;AACvB;AAEA,MAAMC,WAAW,SAASf,SAAS,CAAC;EAClCgB,WAAWA,CAACX,KAAK,EAAE;IACjB,KAAK,CAAC,CAAC;;IAEP;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACY,MAAM,GAAGZ,KAAK;EACrB;EAEAa,MAAMA,CAACd,SAAS,EAAE;IAChB,IAAI,CAAC,IAAI,CAACe,IAAI,EAAE;MACd,MAAMC,MAAM,GAAG,IAAIlB,SAAS,CAAC,CAAC;MAC9BC,KAAK,CAACiB,MAAM,EAAE,IAAI,CAACH,MAAM,CAAC;MAC1B,IAAI,CAACE,IAAI,GAAGC,MAAM,CAACC,GAAG;IACxB;IACAjB,SAAS,CAACkB,QAAQ,CAAC,IAAI,CAACH,IAAI,CAAC;EAC/B;EAEAI,SAASA,CAAA,EAAG;IACV,OAAO,IAAI;EACb;EAEAC,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAACC,IAAI,EAAE;IACf,QAAQA,IAAI;MACV,KAAK,IAAI,CAACT,MAAM,CAACV,GAAG;QAClB,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;AACF;AAEAoB,MAAM,CAACC,OAAO,GAAGb,WAAW"}
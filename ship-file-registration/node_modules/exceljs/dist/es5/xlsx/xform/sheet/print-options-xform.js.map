{"version": 3, "file": "print-options-xform.js", "names": ["_", "require", "BaseXform", "booleanToXml", "model", "undefined", "PrintOptionsXform", "tag", "render", "xmlStream", "attributes", "headings", "showRowColHeaders", "gridLines", "showGridLines", "horizontalCentered", "verticalCentered", "some", "value", "leafNode", "parseOpen", "node", "name", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/print-options-xform.js"], "sourcesContent": ["const _ = require('../../../utils/under-dash');\nconst BaseXform = require('../base-xform');\n\nfunction booleanToXml(model) {\n  return model ? '1' : undefined;\n}\n\nclass PrintOptionsXform extends BaseXform {\n  get tag() {\n    return 'printOptions';\n  }\n\n  render(xmlStream, model) {\n    if (model) {\n      const attributes = {\n        headings: booleanToXml(model.showRowColHeaders),\n        gridLines: booleanToXml(model.showGridLines),\n        horizontalCentered: booleanToXml(model.horizontalCentered),\n        verticalCentered: booleanToXml(model.verticalCentered),\n      };\n      if (_.some(attributes, value => value !== undefined)) {\n        xmlStream.leafNode(this.tag, attributes);\n      }\n    }\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case this.tag:\n        this.model = {\n          showRowColHeaders: node.attributes.headings === '1',\n          showGridLines: node.attributes.gridLines === '1',\n          horizontalCentered: node.attributes.horizontalCentered === '1',\n          verticalCentered: node.attributes.verticalCentered === '1',\n        };\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = PrintOptionsXform;\n"], "mappings": ";;AAAA,MAAMA,CAAC,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAC9C,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAE1C,SAASE,YAAYA,CAACC,KAAK,EAAE;EAC3B,OAAOA,KAAK,GAAG,GAAG,GAAGC,SAAS;AAChC;AAEA,MAAMC,iBAAiB,SAASJ,SAAS,CAAC;EACxC,IAAIK,GAAGA,CAAA,EAAG;IACR,OAAO,cAAc;EACvB;EAEAC,MAAMA,CAACC,SAAS,EAAEL,KAAK,EAAE;IACvB,IAAIA,KAAK,EAAE;MACT,MAAMM,UAAU,GAAG;QACjBC,QAAQ,EAAER,YAAY,CAACC,KAAK,CAACQ,iBAAiB,CAAC;QAC/CC,SAAS,EAAEV,YAAY,CAACC,KAAK,CAACU,aAAa,CAAC;QAC5CC,kBAAkB,EAAEZ,YAAY,CAACC,KAAK,CAACW,kBAAkB,CAAC;QAC1DC,gBAAgB,EAAEb,YAAY,CAACC,KAAK,CAACY,gBAAgB;MACvD,CAAC;MACD,IAAIhB,CAAC,CAACiB,IAAI,CAACP,UAAU,EAAEQ,KAAK,IAAIA,KAAK,KAAKb,SAAS,CAAC,EAAE;QACpDI,SAAS,CAACU,QAAQ,CAAC,IAAI,CAACZ,GAAG,EAAEG,UAAU,CAAC;MAC1C;IACF;EACF;EAEAU,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,IAAI,CAACf,GAAG;QACX,IAAI,CAACH,KAAK,GAAG;UACXQ,iBAAiB,EAAES,IAAI,CAACX,UAAU,CAACC,QAAQ,KAAK,GAAG;UACnDG,aAAa,EAAEO,IAAI,CAACX,UAAU,CAACG,SAAS,KAAK,GAAG;UAChDE,kBAAkB,EAAEM,IAAI,CAACX,UAAU,CAACK,kBAAkB,KAAK,GAAG;UAC9DC,gBAAgB,EAAEK,IAAI,CAACX,UAAU,CAACM,gBAAgB,KAAK;QACzD,CAAC;QACD,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAO,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGpB,iBAAiB"}
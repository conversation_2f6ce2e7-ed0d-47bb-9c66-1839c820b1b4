{"version": 3, "file": "table-xform.js", "names": ["XmlStream", "require", "BaseXform", "ListXform", "AutoFilterXform", "TableColumnXform", "TableStyleInfoXform", "TableXform", "constructor", "map", "autoFilter", "tableColumns", "tag", "count", "empty", "childXform", "tableStyleInfo", "prepare", "model", "options", "columns", "render", "xmlStream", "openXml", "StdDocAttributes", "openNode", "TABLE_ATTRIBUTES", "id", "name", "displayName", "ref", "tableRef", "totalsRowCount", "totalsRow", "undefined", "totalsRowShown", "headerRowCount", "headerRow", "style", "closeNode", "parseOpen", "node", "parser", "attributes", "reset", "parseText", "text", "parseClose", "autoFilterRef", "for<PERSON>ach", "column", "index", "filterButton", "reconcile", "dxfId", "styles", "getDxfStyle", "xmlns", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/table/table-xform.js"], "sourcesContent": ["const XmlStream = require('../../../utils/xml-stream');\n\nconst BaseXform = require('../base-xform');\nconst ListXform = require('../list-xform');\n\nconst AutoFilterXform = require('./auto-filter-xform');\nconst TableColumnXform = require('./table-column-xform');\nconst TableStyleInfoXform = require('./table-style-info-xform');\n\nclass TableXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      autoFilter: new AutoFilterXform(),\n      tableColumns: new ListXform({\n        tag: 'tableColumns',\n        count: true,\n        empty: true,\n        childXform: new TableColumnXform(),\n      }),\n      tableStyleInfo: new TableStyleInfoXform(),\n    };\n  }\n\n  prepare(model, options) {\n    this.map.autoFilter.prepare(model);\n    this.map.tableColumns.prepare(model.columns, options);\n  }\n\n  get tag() {\n    return 'table';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openXml(XmlStream.StdDocAttributes);\n    xmlStream.openNode(this.tag, {\n      ...TableXform.TABLE_ATTRIBUTES,\n      id: model.id,\n      name: model.name,\n      displayName: model.displayName || model.name,\n      ref: model.tableRef,\n      totalsRowCount: model.totalsRow ? '1' : undefined,\n      totalsRowShown: model.totalsRow ? undefined : '1',\n      headerRowCount: model.headerRow ? '1' : '0',\n    });\n\n    this.map.autoFilter.render(xmlStream, model);\n    this.map.tableColumns.render(xmlStream, model.columns);\n    this.map.tableStyleInfo.render(xmlStream, model.style);\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    const {name, attributes} = node;\n    switch (name) {\n      case this.tag:\n        this.reset();\n        this.model = {\n          name: attributes.name,\n          displayName: attributes.displayName || attributes.name,\n          tableRef: attributes.ref,\n          totalsRow: attributes.totalsRowCount === '1',\n          headerRow: attributes.headerRowCount === '1',\n        };\n        break;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n        }\n        break;\n    }\n    return true;\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case this.tag:\n        this.model.columns = this.map.tableColumns.model;\n        if (this.map.autoFilter.model) {\n          this.model.autoFilterRef = this.map.autoFilter.model.autoFilterRef;\n          this.map.autoFilter.model.columns.forEach((column, index) => {\n            this.model.columns[index].filterButton = column.filterButton;\n          });\n        }\n        this.model.style = this.map.tableStyleInfo.model;\n        return false;\n      default:\n        // could be some unrecognised tags\n        return true;\n    }\n  }\n\n  reconcile(model, options) {\n    // fetch the dfxs from styles\n    model.columns.forEach(column => {\n      if (column.dxfId !== undefined) {\n        column.style = options.styles.getDxfStyle(column.dxfId);\n      }\n    });\n  }\n}\n\nTableXform.TABLE_ATTRIBUTES = {\n  xmlns: 'http://schemas.openxmlformats.org/spreadsheetml/2006/main',\n  'xmlns:mc': 'http://schemas.openxmlformats.org/markup-compatibility/2006',\n  'mc:Ignorable': 'xr xr3',\n  'xmlns:xr': 'http://schemas.microsoft.com/office/spreadsheetml/2014/revision',\n  'xmlns:xr3': 'http://schemas.microsoft.com/office/spreadsheetml/2016/revision3',\n  // 'xr:uid': '{00000000-000C-0000-FFFF-FFFF00000000}',\n};\n\nmodule.exports = TableXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAEtD,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAME,SAAS,GAAGF,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMG,eAAe,GAAGH,OAAO,CAAC,qBAAqB,CAAC;AACtD,MAAMI,gBAAgB,GAAGJ,OAAO,CAAC,sBAAsB,CAAC;AACxD,MAAMK,mBAAmB,GAAGL,OAAO,CAAC,0BAA0B,CAAC;AAE/D,MAAMM,UAAU,SAASL,SAAS,CAAC;EACjCM,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACTC,UAAU,EAAE,IAAIN,eAAe,CAAC,CAAC;MACjCO,YAAY,EAAE,IAAIR,SAAS,CAAC;QAC1BS,GAAG,EAAE,cAAc;QACnBC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,IAAI;QACXC,UAAU,EAAE,IAAIV,gBAAgB,CAAC;MACnC,CAAC,CAAC;MACFW,cAAc,EAAE,IAAIV,mBAAmB,CAAC;IAC1C,CAAC;EACH;EAEAW,OAAOA,CAACC,KAAK,EAAEC,OAAO,EAAE;IACtB,IAAI,CAACV,GAAG,CAACC,UAAU,CAACO,OAAO,CAACC,KAAK,CAAC;IAClC,IAAI,CAACT,GAAG,CAACE,YAAY,CAACM,OAAO,CAACC,KAAK,CAACE,OAAO,EAAED,OAAO,CAAC;EACvD;EAEA,IAAIP,GAAGA,CAAA,EAAG;IACR,OAAO,OAAO;EAChB;EAEAS,MAAMA,CAACC,SAAS,EAAEJ,KAAK,EAAE;IACvBI,SAAS,CAACC,OAAO,CAACvB,SAAS,CAACwB,gBAAgB,CAAC;IAC7CF,SAAS,CAACG,QAAQ,CAAC,IAAI,CAACb,GAAG,EAAE;MAC3B,GAAGL,UAAU,CAACmB,gBAAgB;MAC9BC,EAAE,EAAET,KAAK,CAACS,EAAE;MACZC,IAAI,EAAEV,KAAK,CAACU,IAAI;MAChBC,WAAW,EAAEX,KAAK,CAACW,WAAW,IAAIX,KAAK,CAACU,IAAI;MAC5CE,GAAG,EAAEZ,KAAK,CAACa,QAAQ;MACnBC,cAAc,EAAEd,KAAK,CAACe,SAAS,GAAG,GAAG,GAAGC,SAAS;MACjDC,cAAc,EAAEjB,KAAK,CAACe,SAAS,GAAGC,SAAS,GAAG,GAAG;MACjDE,cAAc,EAAElB,KAAK,CAACmB,SAAS,GAAG,GAAG,GAAG;IAC1C,CAAC,CAAC;IAEF,IAAI,CAAC5B,GAAG,CAACC,UAAU,CAACW,MAAM,CAACC,SAAS,EAAEJ,KAAK,CAAC;IAC5C,IAAI,CAACT,GAAG,CAACE,YAAY,CAACU,MAAM,CAACC,SAAS,EAAEJ,KAAK,CAACE,OAAO,CAAC;IACtD,IAAI,CAACX,GAAG,CAACO,cAAc,CAACK,MAAM,CAACC,SAAS,EAAEJ,KAAK,CAACoB,KAAK,CAAC;IAEtDhB,SAAS,CAACiB,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,MAAM;MAACb,IAAI;MAAEe;IAAU,CAAC,GAAGF,IAAI;IAC/B,QAAQb,IAAI;MACV,KAAK,IAAI,CAAChB,GAAG;QACX,IAAI,CAACgC,KAAK,CAAC,CAAC;QACZ,IAAI,CAAC1B,KAAK,GAAG;UACXU,IAAI,EAAEe,UAAU,CAACf,IAAI;UACrBC,WAAW,EAAEc,UAAU,CAACd,WAAW,IAAIc,UAAU,CAACf,IAAI;UACtDG,QAAQ,EAAEY,UAAU,CAACb,GAAG;UACxBG,SAAS,EAAEU,UAAU,CAACX,cAAc,KAAK,GAAG;UAC5CK,SAAS,EAAEM,UAAU,CAACP,cAAc,KAAK;QAC3C,CAAC;QACD;MACF;QACE,IAAI,CAACM,MAAM,GAAG,IAAI,CAACjC,GAAG,CAACgC,IAAI,CAACb,IAAI,CAAC;QACjC,IAAI,IAAI,CAACc,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC7B;QACA;IACJ;IACA,OAAO,IAAI;EACb;EAEAI,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACJ,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACG,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACnB,IAAI,EAAE;IACf,IAAI,IAAI,CAACc,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACK,UAAU,CAACnB,IAAI,CAAC,EAAE;QACjC,IAAI,CAACc,MAAM,GAAGR,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQN,IAAI;MACV,KAAK,IAAI,CAAChB,GAAG;QACX,IAAI,CAACM,KAAK,CAACE,OAAO,GAAG,IAAI,CAACX,GAAG,CAACE,YAAY,CAACO,KAAK;QAChD,IAAI,IAAI,CAACT,GAAG,CAACC,UAAU,CAACQ,KAAK,EAAE;UAC7B,IAAI,CAACA,KAAK,CAAC8B,aAAa,GAAG,IAAI,CAACvC,GAAG,CAACC,UAAU,CAACQ,KAAK,CAAC8B,aAAa;UAClE,IAAI,CAACvC,GAAG,CAACC,UAAU,CAACQ,KAAK,CAACE,OAAO,CAAC6B,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;YAC3D,IAAI,CAACjC,KAAK,CAACE,OAAO,CAAC+B,KAAK,CAAC,CAACC,YAAY,GAAGF,MAAM,CAACE,YAAY;UAC9D,CAAC,CAAC;QACJ;QACA,IAAI,CAAClC,KAAK,CAACoB,KAAK,GAAG,IAAI,CAAC7B,GAAG,CAACO,cAAc,CAACE,KAAK;QAChD,OAAO,KAAK;MACd;QACE;QACA,OAAO,IAAI;IACf;EACF;EAEAmC,SAASA,CAACnC,KAAK,EAAEC,OAAO,EAAE;IACxB;IACAD,KAAK,CAACE,OAAO,CAAC6B,OAAO,CAACC,MAAM,IAAI;MAC9B,IAAIA,MAAM,CAACI,KAAK,KAAKpB,SAAS,EAAE;QAC9BgB,MAAM,CAACZ,KAAK,GAAGnB,OAAO,CAACoC,MAAM,CAACC,WAAW,CAACN,MAAM,CAACI,KAAK,CAAC;MACzD;IACF,CAAC,CAAC;EACJ;AACF;AAEA/C,UAAU,CAACmB,gBAAgB,GAAG;EAC5B+B,KAAK,EAAE,2DAA2D;EAClE,UAAU,EAAE,6DAA6D;EACzE,cAAc,EAAE,QAAQ;EACxB,UAAU,EAAE,iEAAiE;EAC7E,WAAW,EAAE;EACb;AACF,CAAC;;AAEDC,MAAM,CAACC,OAAO,GAAGpD,UAAU"}
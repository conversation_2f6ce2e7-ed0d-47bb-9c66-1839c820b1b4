{"version": 3, "file": "table-style-info-xform.js", "names": ["BaseXform", "require", "TableStyleInfoXform", "tag", "render", "xmlStream", "model", "leafNode", "name", "theme", "undefined", "showFirstColumn", "showLastColumn", "showRowStripes", "showColumnStripes", "parseOpen", "node", "attributes", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/table/table-style-info-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass TableStyleInfoXform extends BaseXform {\n  get tag() {\n    return 'tableStyleInfo';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.leafNode(this.tag, {\n      name: model.theme ? model.theme : undefined,\n      showFirstColumn: model.showFirstColumn ? '1' : '0',\n      showLastColumn: model.showLastColumn ? '1' : '0',\n      showRowStripes: model.showRowStripes ? '1' : '0',\n      showColumnStripes: model.showColumnStripes ? '1' : '0',\n    });\n    return true;\n  }\n\n  parseOpen(node) {\n    if (node.name === this.tag) {\n      const {attributes} = node;\n      this.model = {\n        theme: attributes.name ? attributes.name : null,\n        showFirstColumn: attributes.showFirstColumn === '1',\n        showLastColumn: attributes.showLastColumn === '1',\n        showRowStripes: attributes.showRowStripes === '1',\n        showColumnStripes: attributes.showColumnStripes === '1',\n      };\n      return true;\n    }\n    return false;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = TableStyleInfoXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,mBAAmB,SAASF,SAAS,CAAC;EAC1C,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,gBAAgB;EACzB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,EAAE;MAC3BK,IAAI,EAAEF,KAAK,CAACG,KAAK,GAAGH,KAAK,CAACG,KAAK,GAAGC,SAAS;MAC3CC,eAAe,EAAEL,KAAK,CAACK,eAAe,GAAG,GAAG,GAAG,GAAG;MAClDC,cAAc,EAAEN,KAAK,CAACM,cAAc,GAAG,GAAG,GAAG,GAAG;MAChDC,cAAc,EAAEP,KAAK,CAACO,cAAc,GAAG,GAAG,GAAG,GAAG;MAChDC,iBAAiB,EAAER,KAAK,CAACQ,iBAAiB,GAAG,GAAG,GAAG;IACrD,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACR,IAAI,KAAK,IAAI,CAACL,GAAG,EAAE;MAC1B,MAAM;QAACc;MAAU,CAAC,GAAGD,IAAI;MACzB,IAAI,CAACV,KAAK,GAAG;QACXG,KAAK,EAAEQ,UAAU,CAACT,IAAI,GAAGS,UAAU,CAACT,IAAI,GAAG,IAAI;QAC/CG,eAAe,EAAEM,UAAU,CAACN,eAAe,KAAK,GAAG;QACnDC,cAAc,EAAEK,UAAU,CAACL,cAAc,KAAK,GAAG;QACjDC,cAAc,EAAEI,UAAU,CAACJ,cAAc,KAAK,GAAG;QACjDC,iBAAiB,EAAEG,UAAU,CAACH,iBAAiB,KAAK;MACtD,CAAC;MACD,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAI,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGnB,mBAAmB"}
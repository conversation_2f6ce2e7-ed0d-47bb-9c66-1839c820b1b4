{"version": 3, "file": "auto-filter-xform.js", "names": ["BaseXform", "require", "FilterColumnXform", "AutoFilterXform", "constructor", "map", "filterColumn", "tag", "prepare", "model", "columns", "for<PERSON>ach", "column", "index", "render", "xmlStream", "openNode", "ref", "autoFilterRef", "closeNode", "parseOpen", "node", "parser", "name", "attributes", "Error", "JSON", "stringify", "parseText", "text", "parseClose", "push", "undefined", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/table/auto-filter-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nconst FilterColumnXform = require('./filter-column-xform');\n\nclass AutoFilterXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      filterColumn: new FilterColumnXform(),\n    };\n  }\n\n  get tag() {\n    return 'autoFilter';\n  }\n\n  prepare(model) {\n    model.columns.forEach((column, index) => {\n      this.map.filterColumn.prepare(column, {index});\n    });\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag, {ref: model.autoFilterRef});\n\n    model.columns.forEach(column => {\n      this.map.filterColumn.render(xmlStream, column);\n    });\n\n    xmlStream.closeNode();\n    return true;\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case this.tag:\n        this.model = {\n          autoFilterRef: node.attributes.ref,\n          columns: [],\n        };\n        return true;\n\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parseOpen(node);\n          return true;\n        }\n        throw new Error(`Unexpected xml node in parseOpen: ${JSON.stringify(node)}`);\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.model.columns.push(this.parser.model);\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case this.tag:\n        return false;\n      default:\n        throw new Error(`Unexpected xml node in parseClose: ${name}`);\n    }\n  }\n}\n\nmodule.exports = AutoFilterXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,iBAAiB,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAE1D,MAAME,eAAe,SAASH,SAAS,CAAC;EACtCI,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACTC,YAAY,EAAE,IAAIJ,iBAAiB,CAAC;IACtC,CAAC;EACH;EAEA,IAAIK,GAAGA,CAAA,EAAG;IACR,OAAO,YAAY;EACrB;EAEAC,OAAOA,CAACC,KAAK,EAAE;IACbA,KAAK,CAACC,OAAO,CAACC,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;MACvC,IAAI,CAACR,GAAG,CAACC,YAAY,CAACE,OAAO,CAACI,MAAM,EAAE;QAACC;MAAK,CAAC,CAAC;IAChD,CAAC,CAAC;EACJ;EAEAC,MAAMA,CAACC,SAAS,EAAEN,KAAK,EAAE;IACvBM,SAAS,CAACC,QAAQ,CAAC,IAAI,CAACT,GAAG,EAAE;MAACU,GAAG,EAAER,KAAK,CAACS;IAAa,CAAC,CAAC;IAExDT,KAAK,CAACC,OAAO,CAACC,OAAO,CAACC,MAAM,IAAI;MAC9B,IAAI,CAACP,GAAG,CAACC,YAAY,CAACQ,MAAM,CAACC,SAAS,EAAEH,MAAM,CAAC;IACjD,CAAC,CAAC;IAEFG,SAAS,CAACI,SAAS,CAAC,CAAC;IACrB,OAAO,IAAI;EACb;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,IAAI,CAAChB,GAAG;QACX,IAAI,CAACE,KAAK,GAAG;UACXS,aAAa,EAAEG,IAAI,CAACG,UAAU,CAACP,GAAG;UAClCP,OAAO,EAAE;QACX,CAAC;QACD,OAAO,IAAI;MAEb;QACE,IAAI,CAACY,MAAM,GAAG,IAAI,CAACjB,GAAG,CAACgB,IAAI,CAACE,IAAI,CAAC;QACjC,IAAI,IAAI,CAACD,MAAM,EAAE;UACf,IAAI,CAACF,SAAS,CAACC,IAAI,CAAC;UACpB,OAAO,IAAI;QACb;QACA,MAAM,IAAII,KAAK,CAAE,qCAAoCC,IAAI,CAACC,SAAS,CAACN,IAAI,CAAE,EAAC,CAAC;IAChF;EACF;EAEAO,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACP,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACM,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACP,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACQ,UAAU,CAACP,IAAI,CAAC,EAAE;QACjC,IAAI,CAACd,KAAK,CAACC,OAAO,CAACqB,IAAI,CAAC,IAAI,CAACT,MAAM,CAACb,KAAK,CAAC;QAC1C,IAAI,CAACa,MAAM,GAAGU,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQT,IAAI;MACV,KAAK,IAAI,CAAChB,GAAG;QACX,OAAO,KAAK;MACd;QACE,MAAM,IAAIkB,KAAK,CAAE,sCAAqCF,IAAK,EAAC,CAAC;IACjE;EACF;AACF;AAEAU,MAAM,CAACC,OAAO,GAAG/B,eAAe"}
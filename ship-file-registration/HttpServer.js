"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpServer = void 0;
const express_1 = __importDefault(require("express"));
const bodyParser = __importStar(require("body-parser"));
const serve_static_1 = __importDefault(require("serve-static"));
const path = __importStar(require("path"));
class HttpServer {
    constructor(_port) {
        this._port = _port;
        this._app = (0, express_1.default)();
        this._app.use(bodyParser.urlencoded({ extended: false }));
        this._app.use(bodyParser.json({ limit: "10000kb" }));
        this._app.use("/tmp", (0, serve_static_1.default)(path.join(__dirname, './tmp')));
        this._app.use(function (req, res, next) {
            next();
        });
    }
    BindingRouter(router) {
        this._app.use('/api', router);
    }
    Startup() {
        this.logError();
        let server = this._app.listen(this._port, () => {
            let address = server.address();
            if (address) {
                if (typeof address === 'string')
                    console.log(`HttpServer start http://${address}`);
                else
                    console.log(`HttpServer start http://${address.address}:${address.port}`);
            }
            else
                console.error('HttServer address is null');
        });
    }
    logError() {
        this._app.use(function (error, req, res, next) {
            if (typeof error === 'number')
                res.status(error);
            else
                res.status(500).json({
                    code: error.code,
                    message: error.message,
                    stack: error.stack,
                    data: error.data
                });
            next();
        });
    }
}
exports.HttpServer = HttpServer;
//# sourceMappingURL=HttpServer.js.map
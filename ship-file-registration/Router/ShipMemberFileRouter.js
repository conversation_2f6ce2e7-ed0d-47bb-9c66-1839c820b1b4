"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KeyPeoplecontrast = void 0;
const RouterBase_1 = require("./RouterBase");
const express_1 = __importDefault(require("express"));
const CommonUtil_1 = require("../Util/CommonUtil");
const node_xlsx_1 = __importDefault(require("node-xlsx"));
const multer_1 = __importDefault(require("multer"));
const bent_1 = __importDefault(require("bent"));
class ShipMemberFileRouter extends RouterBase_1.RouterBase {
    get Router() {
        return this.router_;
    }
    constructor(action_) {
        super();
        this.action_ = action_;
        const upload = (0, multer_1.default)({
            fileFilter: (req, file, cb) => {
                cb(null, true);
            }
        });
        this.router_ = express_1.default.Router();
        this.router_.get('/shipRegistration/shipMember', this.table_.bind(this));
        this.router_.post('/shipRegistration/shipMember', this.save_.bind(this));
        this.router_.put('/shipRegistration/shipMember', this.update_.bind(this));
        this.router_.delete('/shipRegistration/shipMember', this.delete_.bind(this));
        this.router_.get('/shipRegistration/shipMemberhistory', this.shipMemberhistory.bind(this));
        this.router_.get('/shipRegistration/addShipMember', this.addShipMemberTable_.bind(this));
        this.router_.put('/shipRegistration/addShipMember', this.addShipMember_.bind(this));
        this.router_.put('/shipRegistration/delShipMember', this.deleteShipMember_.bind(this));
        //this.router_.post('/shipRegistration/shipMemberFileUpload', this.shipMemberFileUpload_.bind(this));
        this.router_.post('/shipRegistration/shipMemberFileUpload', upload.single("file"), this.importExcel.bind(this));
        this.router_.post('/insertCrewInfo', this.insertCrewInfo_.bind(this));
        this.router_.post('/shipRegistration/updateOrg', this.updateOrg.bind(this));
        this.router_.put('/shipRegistration/restore', this.restore_.bind(this));
    }
    table_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.action_.table(req.query);
            this.sendOKResponse(res, result);
        });
    }
    save_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            if (req.body.id_number) {
                const result = yield this.action_.getByIdNumber(req.body.id_number);
                if (result.length > 0) {
                    let id = [];
                    result.map(item => {
                        id.push(item.id);
                    });
                    const data = yield this.action_.delete(id);
                }
            }
            const result = yield this.action_.save(req.body);
            if (req.body.bindShip) {
                let memberHistory = {
                    ship_id: req.body.ship_id,
                    bound_ship_name: req.body.bound_ship_name,
                    ship_mmsi: req.body.ship_mmsi,
                    ship_operation_code: req.body.ship_operation_code,
                    id: result,
                    id_number: req.body.id_number,
                    name: req.body.name,
                    relationship: req.body.relationship,
                };
                try {
                    this.action_.addShipMemberHistory(memberHistory);
                }
                catch (e) {
                    console.log('初次添加船员，增加历史船员记录错误，', memberHistory);
                }
            }
            this.sendOKResponse(res, result);
        });
    }
    update_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.action_.update(req.body);
            if (req.body.bindShip) {
                let memberHistory = {
                    ship_id: req.body.ship_id,
                    bound_ship_name: req.body.bound_ship_name,
                    ship_mmsi: req.body.ship_mmsi,
                    ship_operation_code: req.body.ship_operation_code,
                    id: req.body.id,
                    id_number: req.body.id_number,
                    name: req.body.name,
                    relationship: req.body.relationship,
                };
                try {
                    this.action_.addShipMemberHistory(memberHistory);
                }
                catch (e) {
                    console.log('修改船员，增加历史船员记录错误，', memberHistory);
                }
            }
            this.sendOKResponse(res, result);
        });
    }
    delete_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            let data = req.query.data;
            if (data && data.length > 0) {
                const result = yield this.action_.delete(data);
                this.sendOKResponse(res, result);
            }
            else {
                this.sendResponse(500, res, "缺失必要参数");
            }
        });
    }
    restore_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            let data = req.body.data;
            const result = yield this.action_.restore_(data);
            this.sendOKResponse(res, result);
        });
    }
    shipMemberhistory(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            let pageIndex, pageSize;
            if (!(0, CommonUtil_1.isUndefined_Empty)(req.query['pageIndex'])) {
                pageIndex = req.query['pageIndex'];
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(req.query['pageSize'])) {
                pageSize = req.query['pageSize'];
            }
            if (!pageIndex || !pageSize) {
                this.sendResponse(500, res, "缺失必要参数");
            }
            else {
                const result = yield this.action_.shipMemberhistory(req.query);
                this.sendOKResponse(res, result);
            }
        });
    }
    addShipMemberTable_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            let pageIndex, pageSize;
            if (!(0, CommonUtil_1.isUndefined_Empty)(req.query['pageIndex'])) {
                pageIndex = req.query['pageIndex'];
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(req.query['pageSize'])) {
                pageSize = req.query['pageSize'];
            }
            if (!pageIndex || !pageSize) {
                this.sendResponse(500, res, "缺失必要参数");
            }
            else {
                const result = yield this.action_.addShipMemberTable(req.query);
                this.sendOKResponse(res, result);
            }
        });
    }
    addShipMember_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.action_.addShipMember(req.body);
            this.sendOKResponse(res, result);
        });
    }
    deleteShipMember_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.action_.deleteShipMember(req.body);
            this.sendOKResponse(res, result);
        });
    }
    // async shipMemberFileUpload_(req: express.Request, res: express.Response, next: any) {
    //     const result = await this.action_.shipMemberFileUpload(req.body);
    //     this.sendOKResponse(res, result);
    // }
    insertCrewInfo_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let crewData = req.body;
                let result = yield this.action_.insertCrewInfo(crewData);
                this.sendOKResponse(res, result);
            }
            catch (e) {
                res.status(500).json(e);
            }
        });
    }
    updateOrg(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let data = req.body;
                let result = yield this.action_.updateOrg(data);
                this.sendOKResponse(res, result);
            }
            catch (e) {
                res.status(500).json(e);
            }
        });
    }
    importExcel(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                if (req.file) {
                    const workbook = node_xlsx_1.default.parse(req.file.buffer);
                    let repeatData = [];
                    if (workbook.length > 0) {
                        let org_id = req.body.org_id || 0;
                        let org_name = req.body.org_name || '';
                        let region = req.body.region || 0;
                        let org_code = req.body.org_code || '';
                        for (let sheet of workbook) {
                            if (sheet && sheet.name && sheet.data && sheet.data.length > 0) {
                                //sheet为excel文件中每一个sheet页，sheet.name为每个sheet页名称，sheet.data为里面数据，data[0]为表头，从data[1]开始计算数据
                                let total = 0;
                                let sheetData = sheet.data;
                                for (let i = 1; i < sheetData.length; i++) {
                                    if (sheetData[i][0]) {
                                        let item = {
                                            relationship: sheetData[i][0],
                                            nationality: sheetData[i][1],
                                            id_type: sheetData[i][2],
                                            id_number: sheetData[i][3],
                                            name: sheetData[i][4],
                                            gender: sheetData[i][5],
                                            phone: sheetData[i][6],
                                            declaration_type: sheetData[i][7] == '离船' ? 2 : 1,
                                            focus: sheetData[i][8],
                                            census_address: sheetData[i][9],
                                            current_address: sheetData[i][10],
                                            remarks: sheetData[i][11],
                                            org_id: org_id,
                                            org_name: org_name,
                                            region: region,
                                            org_code: org_code,
                                        };
                                        let addData = yield this.action_.table({ id_number: sheetData[i][3] });
                                        //let addData = (await this.action_.listShip(['BASJ_CHCB = ? and CMH_CHCB = ?'],[sheetData[i][23]?sheetData[i][23]:null,sheetData[i][0]],0,1) as any).total > 0;
                                        if (addData.total == 0) {
                                            //await this.action_.addShip(item);
                                            yield this.action_.save(item);
                                        }
                                        else {
                                            repeatData.push(sheetData[i][4]);
                                        }
                                        total++;
                                    }
                                }
                                console.log('成功导入数据------>', sheet.name, '--->', total);
                            }
                        }
                    }
                    res.status(200).json({ repeatData: repeatData });
                }
                else {
                    res.status(400).json("导入失败");
                }
            }
            catch (e) {
                res.status(500).json(e);
                next(e);
            }
        });
    }
}
exports.default = ShipMemberFileRouter;
function KeyPeoplecontrast(id_number, name, gender, phone, current_address) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let url = 'http://127.0.0.1:12015/api/externalRequest/contrast';
            let get = (0, bent_1.default)(url, 'POST', 'json', 200);
            let data = yield get('', {
                dataList: [{
                        id_number: id_number,
                        name: name,
                        gender: gender,
                        phone: phone,
                        current_address: current_address,
                    }],
                source: '船员定时比对'
            });
            return data;
        }
        catch (e) {
            console.log(e, 'KeyPeoplecontrast-重点人员匹配报警');
            return [];
        }
    });
}
exports.KeyPeoplecontrast = KeyPeoplecontrast;
//# sourceMappingURL=ShipMemberFileRouter.js.map
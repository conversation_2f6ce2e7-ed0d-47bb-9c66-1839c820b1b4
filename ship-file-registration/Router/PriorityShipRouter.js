"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const RouterBase_1 = require("./RouterBase");
const express_1 = __importDefault(require("express"));
class PriorityShipRouter extends RouterBase_1.RouterBase {
    get Router() {
        return this.router_;
    }
    constructor(action_) {
        super();
        this.action_ = action_;
        this.router_ = express_1.default.Router();
        this.router_.get('/shipRegistration/priorityShip', this.table_.bind(this));
        this.router_.post('/shipRegistration/priorityShip', this.add_.bind(this));
        this.router_.put('/shipRegistration/priorityShip', this.update_.bind(this));
        this.router_.delete('/shipRegistration/priorityShip', this.delete_.bind(this));
    }
    table_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.action_.table_(req.query);
            this.sendOKResponse(res, result);
        });
    }
    add_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.action_.add_(req.body);
            this.sendOKResponse(res, result);
        });
    }
    update_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.action_.update_(req.body);
            this.sendOKResponse(res, result);
        });
    }
    delete_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.action_.delete_(req.query);
            this.sendOKResponse(res, result);
        });
    }
}
exports.default = PriorityShipRouter;
//# sourceMappingURL=PriorityShipRouter.js.map
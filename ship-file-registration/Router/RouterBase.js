"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RouterBase = void 0;
class RouterBase {
    sendTableResponse(res, offset, limit, total, items) {
        res.status(200).json({ offset, limit, total, items });
    }
    sendOKResponse(res, data) {
        res.status(200).json({ result: data });
    }
    sendResponse(status, res, msg) {
        if (msg instanceof Error)
            msg = msg.message;
        res.status(status).json(msg);
    }
}
exports.RouterBase = RouterBase;
//# sourceMappingURL=RouterBase.js.map
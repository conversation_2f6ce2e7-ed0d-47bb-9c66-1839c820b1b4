"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const RouterBase_1 = require("./RouterBase");
const express_1 = __importDefault(require("express"));
const multer_1 = __importDefault(require("multer"));
const node_xlsx_1 = __importDefault(require("node-xlsx"));
const fs_1 = __importDefault(require("fs"));
const Path = __importStar(require("path"));
const moment_1 = __importDefault(require("moment"));
class ShipFileRouter extends RouterBase_1.RouterBase {
    get Router() {
        return this.router_;
    }
    constructor(action_) {
        super();
        this.action_ = action_;
        this.syncYuChuanJiBenCache = new Set();
        this.syncYuChuanJiBenMMSICache = new Set();
        this.syncYuChuanJiBenShipNameCache = new Set();
        this.config_ = require('../config.json');
        this.router_ = express_1.default.Router();
        const upload = (0, multer_1.default)({
            fileFilter: (req, file, cb) => {
                cb(null, true);
            }
        });
        this.router_.get('/shipRegistration/ship', this.table_.bind(this));
        this.router_.get('/shipRegistration/getShipByMmsi', this.getShipByMmsi.bind(this));
        this.router_.post('/shipRegistration/ship', this.save_.bind(this));
        this.router_.put('/shipRegistration/ship', this.update_.bind(this));
        this.router_.delete('/shipRegistration/ship', this.delete_.bind(this));
        this.router_.post('/shipRegistration/shipFileUpload', upload.single("file"), this.shipFileUpload_.bind(this));
        this.router_.post('/insertShipInfo', this.insertShipInfo_.bind(this));
        this.router_.get('/shipRegistration/inoutcrew', this.inoutcrew_.bind(this));
        this.router_.get('/shipRegistration/inoutcrewHistory', this.inoutcrewHistory_.bind(this));
        this.router_.get('/shipRegistration/shipMmsiList', this.getShipMmsiList_.bind(this));
        this.router_.post('/shipRegistration/updateShipMmsiByShipName', this.updateShipMmsiByShipName_.bind(this));
        this.router_.post('/shipRegistration/syncYuChuanJiBen', this.syncYuChuanJiBen.bind(this));
        this.router_.get('/shipRegistration/loadsyncYuChuanJiBenCache', this.loadsyncYuChuanJiBenCache.bind(this));
    }
    inoutcrew_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.action_.inoutcrew_(req.query);
            this.sendOKResponse(res, result);
        });
    }
    inoutcrewHistory_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.action_.inoutcrewHistory_(req.query);
            this.sendOKResponse(res, result);
        });
    }
    table_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.action_.table(req.query);
            this.sendOKResponse(res, result);
        });
    }
    getShipMmsiList_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.action_.getShipMmsiList();
            this.sendOKResponse(res, result);
        });
    }
    updateShipMmsiByShipName_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield this.action_.updateShipMmsiByShipName(req.body);
                this.sendOKResponse(res, result);
            }
            catch (e) {
                next(e);
            }
        });
    }
    getShipByMmsi(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            let mmsi = req.query.mmsi;
            const result = yield this.action_.getShipByMmsi(mmsi);
            this.sendOKResponse(res, result);
        });
    }
    save_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.action_.save(req.body);
            this.sendOKResponse(res, result);
        });
    }
    update_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.action_.update(req.body);
            this.sendOKResponse(res, result);
        });
    }
    delete_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            let data = req.query.ships;
            if (data && data.length > 0) {
                const result = yield this.action_.delete(data);
                this.sendOKResponse(res, result);
            }
            else {
                this.sendResponse(500, res, "缺失必要参数");
            }
        });
    }
    shipFileUpload_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                if (req.file) {
                    const workbook = node_xlsx_1.default.parse(req.file.buffer);
                    let imageData = yield this.readXLSXImage(req.file.buffer);
                    let imageIndex = 0;
                    let repeatData = [];
                    if (workbook.length > 0) {
                        let org_id = req.body.org_id || 0;
                        let org_name = req.body.org_name || '';
                        let region = req.body.region || 0;
                        let org_code = req.body.org_code || '';
                        let mmsiList = [];
                        for (let sheet of workbook) {
                            let sheetData = sheet.data;
                            for (let i = 1; i < sheetData.length; i++) {
                                sheetData[i][2] && mmsiList.push(sheetData[i][2]);
                            }
                        }
                        repeatData = mmsiList.length > 0 ? yield this.action_.getShipInfo('mmsi', 'where mmsi in (?)', [mmsiList]) : [];
                        for (let sheet of workbook) {
                            if (sheet && sheet.name && sheet.data && sheet.data.length > 0) {
                                //sheet为excel文件中每一个sheet页，sheet.name为每个sheet页名称，sheet.data为里面数据，data[0]为表头，从data[1]开始计算数据
                                let total = 0;
                                let sheetData = sheet.data.filter((tableItem) => !repeatData.find((existDataItem) => existDataItem.mmsi == tableItem[2]));
                                for (let i = 1; i < sheetData.length; i++) {
                                    if (sheetData[i][0]) {
                                        let ship_operation_code = this.getUUID(16, 16);
                                        let item = {
                                            ship_operation_code: ship_operation_code || '',
                                            ship_name: sheetData[i][0] || '',
                                            ship_en_name: sheetData[i][1] || '',
                                            mmsi: sheetData[i][2] || '',
                                            ship_type: sheetData[i][3] || '',
                                            declaration_type: sheetData[i][4] == '注销' ? 2 : 1,
                                            home_port: sheetData[i][5] || '',
                                            ship_status: sheetData[i][6] || '',
                                            host_number_approved: sheetData[i][7] || 0,
                                            ship_length: sheetData[i][8] || 0,
                                            ship_width: sheetData[i][9] || 0,
                                            ship_height: sheetData[i][10] || 0,
                                            ton: sheetData[i][11] || 0,
                                            ship_power: sheetData[i][12] || '',
                                            ship_material: sheetData[i][13] || 0,
                                            ship_person_in_charge_name: sheetData[i][14] || '',
                                            ship_person_in_charge_phone: sheetData[i][15] || '',
                                            ship_company: sheetData[i][16] || '',
                                            ship_use: sheetData[i][17] || '',
                                            ship_character: sheetData[i][18] || '',
                                            ship_source: sheetData[i][19] || '',
                                            operating_type: sheetData[i][20] || '',
                                            construction_manufacturer: sheetData[i][21] || '',
                                            permanent_berth: sheetData[i][22] || '',
                                            identification_code: sheetData[i][23] || '',
                                            remarks: sheetData[i][24] || '',
                                            type_code: this.getShipTypeCode(sheetData[i][3]) || '',
                                            org_id: org_id,
                                            org_name: org_name,
                                            region: region,
                                            org_code: org_code,
                                        };
                                        if (sheetData[i][25]) { //照片
                                            item.image_name = imageData[imageIndex].name;
                                            item.image_url = [
                                                {
                                                    "del": false,
                                                    "name": imageData[imageIndex].name,
                                                    "size": 150,
                                                    "src": "",
                                                    "params": [],
                                                    "fileName": imageData[imageIndex].filesName,
                                                    "url": '/api/filesUrl/shipFileRegistration/' + imageData[imageIndex].filesName
                                                }
                                            ];
                                            imageIndex++;
                                        }
                                        yield this.action_.save(item);
                                    }
                                    total++;
                                }
                                console.log('成功导入数据------>', sheet.name, '--->', total);
                            }
                        }
                    }
                    res.status(200).json({ repeatData: repeatData });
                }
                else {
                    res.status(400).json("导入失败");
                }
            }
            catch (e) {
                res.status(500).json(e);
                next(e);
            }
        });
    }
    insertShipInfo_(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let fileInfo = req.body;
                let result = yield this.action_.insertShipInfo(fileInfo);
                this.sendOKResponse(res, result);
            }
            catch (e) {
                res.status(500).json(e);
            }
        });
    }
    getShipTypeCode(shipType) {
        let type_code = 0;
        switch (true) {
            case (shipType === '公务船'):
                type_code = 905;
                break;
            case (shipType == '游船'):
                type_code = 105;
                break;
            case (shipType == '渡船'):
                type_code = 104;
                break;
            case (shipType == '渔船'):
                type_code = 9001;
                break;
            case (shipType == '高速船'):
                type_code = 106;
                break;
            case (shipType == '危化品运输船'):
                type_code = 302;
                break;
            case (shipType == '普通运输船'):
                type_code = 200;
                break;
            case (shipType == '工程作业船'):
                type_code = 400;
                break;
            case (shipType === '其他'):
                type_code = 0;
                break;
        }
        return type_code;
    }
    getUUID(len, radix) {
        var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
        var uuid = [];
        var i;
        radix = radix || chars.length;
        if (len) {
            for (i = 0; i < len; i++)
                uuid[i] = chars[0 | Math.random() * radix];
        }
        else {
            var r;
            uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
            uuid[14] = '4';
            for (i = 0; i < 36; i++) {
                if (!uuid[i]) {
                    r = 0 | Math.random() * 16;
                    uuid[i] = chars[(i === 19) ? (r & 0x3) | 0x8 : r];
                }
            }
        }
        return uuid.join('');
    }
    readXLSXImage(fileData) {
        return __awaiter(this, void 0, void 0, function* () {
            let ExcelJS = require('exceljs');
            /* let excelFilePath = path.join(__dirname,'./xxxx.xlsx')
    
             const { promisify } = require('util');
             const readFileAsync = promisify(fs.readFile);
             const fileData2 = await readFileAsync(excelFilePath);
     */
            let workbook = new ExcelJS.Workbook();
            let filesNames = [];
            yield workbook.xlsx.load(fileData)
                .then((resData, xx) => {
                if (resData && resData.media && resData.media.length) {
                    for (let item of resData.media) {
                        let filesName = this.getUUID(16, 16) + `.${item.extension}`;
                        filesNames.push({
                            name: item.name + `.${item.extension}`,
                            filesName: filesName
                        });
                        let imagePath = Path.resolve(this.config_.filesUrl, filesName);
                        fs_1.default.writeFileSync(imagePath, item.buffer);
                    }
                }
            });
            return filesNames;
        });
    }
    syncYuChuanJiBen(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            let errdata = '';
            try {
                let syncData = req.body.syncData;
                let updateNum = 0;
                let addNum = 0;
                for (let item of syncData) {
                    errdata = item;
                    item.ship_name = item.ship_name.replace(/\s+/g, ''); //去除空格
                    if (this.syncYuChuanJiBenShipNameCache.has(item.ship_name) || this.syncYuChuanJiBenMMSICache.has(item.mmsi)) {
                        let where = [];
                        let params = [];
                        let update_field = '';
                        let old_field = '';
                        //理论上不存在重复数据，如果存在 只更新第一条 且 在日志里标注，
                        let dataList = yield this.action_.getShipInfo('*', 'where ship_name = ? or  mmsi = ?', [item.ship_name, item.mmsi]);
                        let obj = Object.assign({}, item);
                        for (let key in obj) {
                            if (obj[key] && key != 'registrant_time' && dataList[0][key] != obj[key]) {
                                where.push(key + '=?');
                                params.push(obj[key]);
                                //console.log(key + ": " + obj[key]); // 输出每个属性及其值
                                update_field += key + '=' + obj[key] + ',';
                                old_field += key + '=' + dataList[0][key] + ',';
                            }
                        }
                        if (where.length) {
                            let sql = ' set ' + where.join(',');
                            params = params.concat([item.ship_name, item.mmsi]);
                            yield this.action_.updatePartField(sql, 'where ship_name = ? or  mmsi = ?', params);
                            updateNum++;
                            yield this.action_.updateLog([(0, moment_1.default)(new Date()).format("YYYY-MM-DD HH:mm:ss"), update_field, old_field, dataList[0].ship_operation_code, Number(item.mmsi)]);
                        }
                    }
                    else {
                        yield this.action_.save(item);
                        addNum++;
                    }
                }
                console.log('渔业数据条数：', syncData.length, ';----', '更新档案条数：' + updateNum, ';----', '新增档案条数：' + addNum);
                this.sendOKResponse(res, 'OK');
            }
            catch (e) {
                console.log('问题数据----', errdata);
                res.status(500).json(e);
            }
        });
    }
    loadsyncYuChuanJiBenCache(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let dataList = yield this.action_.getShipInfo('mmsi,ship_name', '', []);
                this.syncYuChuanJiBenCache.clear();
                this.syncYuChuanJiBenMMSICache.clear();
                this.syncYuChuanJiBenShipNameCache.clear();
                for (let item of dataList) {
                    this.syncYuChuanJiBenCache.add(item.ship_name + '-' + item.mmsi);
                    item.ship_name = item.ship_name.replace(/\s+/g, ''); //去除空格
                    this.syncYuChuanJiBenShipNameCache.add(item.ship_name);
                    this.syncYuChuanJiBenMMSICache.add(item.mmsi);
                }
                this.sendOKResponse(res, '缓存更新成功');
            }
            catch (e) {
                res.status(500).json(e);
            }
        });
    }
}
exports.default = ShipFileRouter;
//# sourceMappingURL=ShipFileRouter.js.map
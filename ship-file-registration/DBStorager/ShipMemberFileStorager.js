"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const CommonUtil_1 = require("../Util/CommonUtil");
const FileUitl_1 = __importDefault(require("../Util/FileUitl"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const moment_1 = __importDefault(require("moment"));
class ShipMemberFileStorager {
    constructor(pool_) {
        this.pool_ = pool_;
        this.config_ = require('../config.json');
    }
    getByIdNumber(id_number) {
        return __awaiter(this, void 0, void 0, function* () {
            let sql = `select * from ship_member_registration_info where id_number = ?`;
            return yield this.pool_.loadRows(sql, id_number);
        });
    }
    table(query) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let totalWheres_ = [], pageIndex = 0, pageSize = 0, temp = '';
                if (!(0, CommonUtil_1.isUndefined_Empty)(query['name'])) {
                    totalWheres_.push('name like' + `'%${query['name']}%'`);
                }
                if (!(0, CommonUtil_1.isUndefined_Empty)(query['logout'])) {
                    totalWheres_.push('logout =' + `'${query['logout']}'`);
                }
                if (!(0, CommonUtil_1.isUndefined_Empty)(query['phone'])) {
                    totalWheres_.push('phone like ' + `'%${query['phone']}%'`);
                }
                if (!(0, CommonUtil_1.isUndefined_Empty)(query['relationship'])) {
                    totalWheres_.push('relationship = ' + `'${query['relationship']}'`);
                }
                if (!(0, CommonUtil_1.isUndefined_Empty)(query['ship_operation_code'])) {
                    totalWheres_.push('ship_operation_code like ' + `'%${query['ship_operation_code']}%'`);
                }
                if (!(0, CommonUtil_1.isUndefined_Empty)(query['bound'])) {
                    totalWheres_.push('bound = ' + `'${query['bound']}'`);
                    // query['bound'] == 1 ? totalWheres_.push(`declaration_type = 1`) : totalWheres_.push(`declaration_type = 2`);
                }
                if (!(0, CommonUtil_1.isUndefined_Empty)(query['boundShipName'])) {
                    totalWheres_.push('bound_ship_name like ' + `'%${query['boundShipName']}%'`);
                }
                if (!(0, CommonUtil_1.isUndefined_Empty)(query['id_number'])) {
                    totalWheres_.push('id_number like' + `'%${query['id_number']}%'`);
                }
                if (!(0, CommonUtil_1.isUndefined_Empty)(query['focus'])) {
                    totalWheres_.push('focus = ' + `'${query['focus']}'`);
                }
                if (!(0, CommonUtil_1.isUndefined_Empty)(query['isDel'])) {
                    totalWheres_.push('is_del = ' + `${query['isDel']}`);
                    // query['isDel'] == 0 ? totalWheres_.push(`declaration_type = 1`) : totalWheres_.push(`declaration_type = 2`);
                }
                else {
                    totalWheres_.push('is_del = 0');
                }
                if (!(0, CommonUtil_1.isUndefined_Empty)(query['org_ids'])) {
                    let org_ids = JSON.parse(query['org_ids']);
                    let str = '\'' + org_ids.join('\',\'') + '\'';
                    totalWheres_.push(`org_id in (${str})`);
                }
                if (!(0, CommonUtil_1.isUndefined_Empty)(query['declaration_type'])) {
                    totalWheres_.push(`declaration_type = '${query['declaration_type']}'`);
                }
                // else {
                // totalWheres_.push(`declaration_type = 1`);
                // }
                pageIndex = query['pageIndex'];
                pageSize = query['pageSize'];
                let where = this.getWhereByParams(totalWheres_);
                let total = yield this.pool_.loadScalar(`select count(*) from ship_member_registration_info ${where}`);
                let sql = `select * from ship_member_registration_info ${where} order by id desc`;
                if (pageSize > 0)
                    sql += ` limit ${pageIndex * pageSize},${pageSize}`;
                let items = yield this.pool_.loadRows(sql);
                if (items && items.length) {
                    for (let item of items) {
                        item.image = yield this.file(item.image);
                    }
                }
                return { total, items };
            }
            catch (e) {
                console.log(e);
            }
        });
    }
    save(data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                if (data.image_url) {
                    for (let item of data.image_url) {
                        let fileName = item.fileName || '';
                        if (item.data) {
                            fileName = (yield this.uploadFile_(item)) || '';
                            delete item.data;
                        }
                        item.fileName = fileName;
                        item.url = '/api/filesUrl/shipFileRegistration/' + fileName;
                    }
                    data.image_url = JSON.stringify(data.image_url);
                }
                let bound = data.bound || 0;
                let bound_ship_name = data.bound_ship_name || '';
                let ship_operation_code = data.ship_operation_code;
                let relationship = data.relationship;
                let nationality = data.nationality;
                let id_type = data.id_type;
                let id_number = data.id_number;
                let name = data.name;
                let gender = data.gender;
                let phone = data.phone;
                let affiliated_unit = data.affiliated_unit;
                let census_address = data.census_address || '';
                let current_address = data.current_address || '';
                let remarks = data.remarks || '';
                let registrant_unit_code = data.registrant_unit_code || '';
                let registrant_unit_name = data.registrant_unit_name || '';
                let registrant_user_id = data.registrant_user_id || '';
                let registrant_user_name = data.registrant_user_name || '';
                let registrant_time = data.registrant_time || (0, moment_1.default)(new Date()).format("YYYY-MM-DD HH:mm:ss");
                let fileName = '';
                let nation = data.nation || '';
                let birthday = data.birthday || '';
                let emergency_contact_people = data.emergency_contact_people || '';
                let emergency_contact_phone = data.emergency_contact_phone || '';
                let logout = data.logout || '';
                let focus = data.focus || '否';
                let image_name = data.image_name || '';
                let declaration_type = data.declaration_type || 1;
                let is_del;
                if (declaration_type == 1) {
                    is_del = 0;
                }
                else {
                    is_del = 1;
                }
                let org_id = data.org_id || 0;
                let org_code = data.org_code || '';
                let org_name = data.org_name || '';
                let region = data.region || 0;
                let image_url = data.image_url || '';
                let res = yield this.pool_.query('insert into ship_member_registration_info (ship_operation_code,bound,bound_ship_name, relationship, nationality, id_type, id_number, name, gender, phone, affiliated_unit, census_address,' +
                    ' current_address, remarks, registrant_unit_code, registrant_unit_name, registrant_user_id, registrant_user_name, registrant_time, image,is_del,nation,birthday,emergency_contact_people,' +
                    'emergency_contact_phone,logout,focus,image_name,image_url,declaration_type,org_id,org_code,org_name,region,create_time) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)', [ship_operation_code, bound, bound_ship_name, relationship, nationality, id_type, id_number, name, gender, phone, affiliated_unit, census_address, current_address, remarks, registrant_unit_code,
                    registrant_unit_name, registrant_user_id, registrant_user_name, registrant_time, fileName, is_del, nation, birthday, emergency_contact_people, emergency_contact_phone, logout, focus,
                    image_name, image_url, declaration_type, org_id, org_code, org_name, region, (0, moment_1.default)(new Date()).format("YYYY-MM-DD HH:mm:ss")]);
                /*try {   //重点人员匹配报警查询
                    KeyPeoplecontrast(data.id_number,data.name,data.gender,data.phone,current_address.current_address)
                }catch (e){
                    console.log('KeyPeoplecontrast-错误');
                }*/
                return res.insertId;
            }
            catch (e) {
                console.error("[Save] Error:", e);
                return false;
            }
        });
    }
    update(data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                if (data.image_url) {
                    for (let item of data.image_url) {
                        let fileName = item.fileName || '';
                        if (item.data) {
                            fileName = (yield this.uploadFile_(item)) || '';
                            delete item.data;
                        }
                        item.fileName = fileName;
                        item.url = '/api/filesUrl/shipFileRegistration/' + fileName;
                    }
                    data.image_url = JSON.stringify(data.image_url);
                }
                let id = data.id;
                let bound = data.bound || 0;
                let bound_ship_name = data.bound_ship_name || '';
                let ship_operation_code = data.ship_operation_code;
                let relationship = data.relationship;
                let nationality = data.nationality;
                let id_type = data.id_type;
                let id_number = data.id_number;
                let name = data.name;
                let gender = data.gender;
                let phone = data.phone;
                let affiliated_unit = data.affiliated_unit;
                let census_address = data.census_address || '';
                let current_address = data.current_address || '';
                let remarks = data.remarks || '';
                let registrant_unit_code = data.registrant_unit_code || '';
                let registrant_unit_name = data.registrant_unit_name || '';
                let registrant_user_id = data.registrant_user_id || '';
                let registrant_user_name = data.registrant_user_name || '';
                let registrant_time = data.registrant_time || (0, moment_1.default)(new Date()).format("YYYY-MM-DD HH:mm:ss");
                let fileName = '';
                let nation = data.nation || '';
                let birthday = data.birthday || '';
                let emergency_contact_people = data.emergency_contact_people || '';
                let emergency_contact_phone = data.emergency_contact_phone || '';
                let logout = data.logout || '';
                let focus = data.focus || '否';
                let image_name = data.image_name || '';
                let image_url = data.image_url || '';
                let declaration_type = data.declaration_type || 1;
                let is_del;
                if (declaration_type == 1) {
                    is_del = 0;
                }
                else {
                    is_del = 1;
                }
                let org_id = data.org_id || 0;
                let org_code = data.org_code || '';
                let org_name = data.org_name || '';
                let region = data.region || 0;
                yield this.pool_.query('update ship_member_registration_info set bound=?,ship_operation_code=?,bound_ship_name=?, relationship=?, nationality=?, id_type=?, id_number=?, name=?, gender=?, phone=?, affiliated_unit=?, ' +
                    'census_address=?, current_address=?, remarks=?, registrant_unit_code=?, registrant_unit_name=?, registrant_user_id=?, registrant_user_name=?, registrant_time=?, image=?,nation=?,' +
                    ' birthday=?, emergency_contact_people=?, emergency_contact_phone=?, logout=?, focus=?, image_name=?, image_url=?,declaration_type=?,is_del=?,org_id=?,org_code=?,org_name=?,region=? where id=?', [bound, ship_operation_code, bound_ship_name, relationship, nationality, id_type, id_number, name, gender, phone, affiliated_unit, census_address, current_address, remarks, registrant_unit_code,
                    registrant_unit_name, registrant_user_id, registrant_user_name, registrant_time, fileName, nation, birthday, emergency_contact_people, emergency_contact_phone, logout, focus, image_name, image_url,
                    declaration_type, is_del, org_id, org_code, org_name, region, id]);
                return true;
            }
            catch (e) {
                console.error("[Update] Error:", e);
                return false;
            }
        });
    }
    delete(data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let members = data;
                for (let i = 0; i < members.length; i++) {
                    yield this.pool_.query('delete from ship_member_registration_info where id = ?', [members[i]]);
                }
                return true;
            }
            catch (e) {
                console.error("[Delete] Error:", e);
                return false;
            }
        });
    }
    restore(data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let members_id = data;
                yield this.pool_.query('update ship_member_registration_info set is_del=0, declaration_type=2 where id = ?', [members_id]);
                return true;
            }
            catch (e) {
                console.error("[Delete] Error:", e);
                return false;
            }
        });
    }
    shipMemberhistory(query) {
        return __awaiter(this, void 0, void 0, function* () {
            let totalWheres_ = [], pageIndex = 0, pageSize = 0, temp = '';
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['ship_name'])) {
                totalWheres_.push('ship_name like' + `'%${query['ship_name']}%'`);
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['crew_name'])) {
                totalWheres_.push('crew_name like' + `'%${query['crew_name']}%'`);
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['ship_id'])) {
                totalWheres_.push('ship_id = ' + `${query['ship_id']}`);
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['crew_id'])) {
                totalWheres_.push('crew_id = ' + `${query['crew_id']}`);
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['is_now'])) {
                totalWheres_.push('is_now = ' + `${query['is_now']}`);
            }
            pageIndex = query['pageIndex'];
            pageSize = query['pageSize'];
            let where = this.getWhereByParams(totalWheres_);
            let total = yield this.pool_.loadScalar(`select count(*) from ship_crew_history ${where}`);
            let sql = `select * from ship_crew_history ${where}`;
            sql += ' ORDER BY resignation_time DESC,entry_time desc ';
            if (pageSize > 0)
                sql += ` limit ${pageIndex * pageSize},${pageSize}`;
            let items = yield this.pool_.loadRows(sql);
            return { total, items };
        });
    }
    addShipMemberTable(query) {
        return __awaiter(this, void 0, void 0, function* () {
            let totalWheres_ = [], pageIndex = 0, pageSize = 0, temp = '';
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['name'])) {
                totalWheres_.push('name like' + `'%${query['name']}%'`);
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['relationship'])) {
                totalWheres_.push('relationship = ' + `'${query['relationship']}'`);
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['id_number'])) {
                totalWheres_.push('id_number = ' + `'${query['id_number']}'`);
            }
            pageIndex = query['pageIndex'];
            pageSize = query['pageSize'];
            let where = this.getWhereByParams(totalWheres_);
            if (where) {
                where += `and ship_operation_code = ''`;
            }
            else {
                where += `where ship_operation_code = ''`;
            }
            where += ` and is_del =0 `;
            let total = yield this.pool_.loadScalar(`select count(*) from ship_member_registration_info ${where}`);
            let sql = `select * from ship_member_registration_info ${where}`;
            if (pageSize > 0)
                sql += ` limit ${pageIndex * pageSize},${pageSize}`;
            let items = yield this.pool_.loadRows(sql);
            return { total, items };
        });
    }
    addShipMember(data) {
        return __awaiter(this, void 0, void 0, function* () {
            //data:{
            // id:number[],船员id的数组
            // time:Date();可选，离船时间，作为离船时间和上船时间
            // shipData:any 船舶信息
            // }
            try {
                let id = data.id;
                let sql = "";
                let params = [];
                let ship_operation_code = data.shipData.ship_operation_code;
                let ship_name = data.shipData.ship_name;
                for (let i = 0; i < id.length; i++) {
                    let crew = yield this.pool_.loadRow('select * from ship_member_registration_info  where id=?', [id[i]]);
                    if (crew) {
                        let history = {
                            id: 0,
                            ship_id: data.shipData.id,
                            ship_name: data.shipData.ship_name,
                            ship_mmsi: data.shipData.mmsi,
                            ship_operation_code: data.shipData.ship_operation_code,
                            crew_id: crew.id,
                            crew_id_number: crew.id_number,
                            crew_name: crew.name,
                            crew_relationship: crew.relationship,
                            entry_time: data.time ? (0, moment_1.default)(new Date(data.time)).format("YYYY-MM-DD HH:mm:ss") : (0, moment_1.default)(new Date()).format("YYYY-MM-DD HH:mm:ss"),
                            resignation_time: null,
                            is_now: 1,
                        };
                        sql += `update ship_crew_history set resignation_time=?,is_now=0 where crew_id=? and is_now=1;`;
                        if (data.time) { //支持传入离船时间，根据进出港申报时，申报时间作为离船时间
                            params.push((0, moment_1.default)(new Date(data.time)).format("YYYY-MM-DD HH:mm:ss"));
                        }
                        else {
                            params.push((0, moment_1.default)(new Date()).format("YYYY-MM-DD HH:mm:ss"));
                        }
                        params.push(history.crew_id);
                        sql += `insert into ship_crew_history(ship_id,ship_name,ship_mmsi,ship_operation_code,crew_id,crew_id_number,crew_name,crew_relationship,entry_time,is_now)values(?,?,?,?,?,?,?,?,?,?);`;
                        params.push(history.ship_id);
                        params.push(history.ship_name);
                        params.push(history.ship_mmsi);
                        params.push(history.ship_operation_code);
                        params.push(history.crew_id);
                        params.push(history.crew_id_number);
                        params.push(history.crew_name);
                        params.push(history.crew_relationship);
                        params.push(history.entry_time);
                        params.push(history.is_now);
                        sql += 'update ship_member_registration_info set ship_operation_code=?, bound=?, bound_ship_name=? ,is_del=0 , declaration_type=1,org_id=?,org_name=?,org_code=?,region=?  where id=?;';
                        params.push(ship_operation_code);
                        params.push(1);
                        params.push(data.shipData.ship_name || null);
                        params.push(data.shipData.org_id || null);
                        params.push(data.shipData.org_name || null);
                        params.push(data.shipData.org_code || null);
                        params.push(data.shipData.region);
                        params.push(id[i]);
                    }
                }
                let result = yield this.pool_.transaction(sql, params);
                return true;
            }
            catch (e) {
                console.log(e);
                return false;
            }
        });
    }
    addShipMemberHistory(data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let sql = `update ship_crew_history set resignation_time=?,is_now=0 where crew_id=? and is_now=1;`;
                let params = [];
                params.push((0, moment_1.default)(new Date()).format("YYYY-MM-DD HH:mm:ss"));
                params.push(data.id);
                sql += `insert into ship_crew_history(ship_id,ship_name,ship_mmsi,ship_operation_code,crew_id,crew_id_number,crew_name,crew_relationship,entry_time,is_now)values(?,?,?,?,?,?,?,?,?,?);`;
                params.push(data.ship_id);
                params.push(data.bound_ship_name);
                params.push(data.ship_mmsi);
                params.push(data.ship_operation_code);
                params.push(data.id);
                params.push(data.id_number);
                params.push(data.name);
                params.push(data.relationship);
                params.push((0, moment_1.default)(new Date()).format("YYYY-MM-DD HH:mm:ss"));
                params.push(1);
                let result = yield this.pool_.transaction(sql, params);
                return true;
            }
            catch (e) {
                console.log(e);
                return false;
            }
        });
    }
    deleteShipMember(data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let id_number = data.data.id_number;
                let bound_ship_name = data.data.bound_ship_name;
                let params = [];
                let sql = 'update ship_member_registration_info set ship_operation_code=?, bound=?, bound_ship_name=?, declaration_type=2 where id_number=?;';
                params.push('');
                params.push(0);
                params.push('');
                params.push(id_number);
                let member = yield this.pool_.loadRow('select * from ship_member_registration_info  where id_number=?', [id_number]);
                if (member) {
                    sql += `update ship_crew_history set resignation_time=?,is_now=0 where crew_id=? and is_now=1;`;
                    if (data.data && data.data.resignation_time) { //支持传入离船时间，根据进出港申报时，申报时间作为离船时间
                        params.push((0, moment_1.default)(new Date(data.data.resignation_time)).format("YYYY-MM-DD HH:mm:ss"));
                    }
                    else {
                        params.push((0, moment_1.default)(new Date()).format("YYYY-MM-DD HH:mm:ss"));
                    }
                    params.push(member.id);
                }
                yield this.pool_.query(sql, params);
                return true;
            }
            catch (e) {
                console.log(e);
                return false;
            }
        });
    }
    // async shipMemberFileUpload(data: any){
    //     try {
    //         if (!Array.isArray(req.body)) {
    //             res.status(200).json(true);
    //         } else if (Array.isArray(req.body)) {
    //             let data = req.body;
    //             if (data[1][4] != '') {
    //                 for (let i = 1; i < data.length; i++) {
    //                     if (data[i][4] == '') {
    //                         continue;
    //                     }
    //                     let registrant_time = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
    //                     let a = await db.loadRow("SELECT 1 FROM ship_member_registration_info where name=?", [data[i][4].toString()]);
    //                     if (a == undefined) {
    //                         let shipMember = await db.query('insert into ship_member_registration_info(relationship, nationality, id_type, id_number, name, gender, phone, affiliated_unit, census_address, ' +
    //                             'current_address, remarks, registrant_unit_code, registrant_unit_name, registrant_user_id, registrant_user_name, registrant_time)' +
    //                             'values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)', [data[i][0], data[i][1], data[i][2], data[i][3], data[i][4], data[i][5], data[i][6], data[i][7], data[i][8], data[i][9], data[i][10], data[i][11], data[i][12], data[i][13], data[i][14], registrant_time]);
    //                     } else {
    //                         continue;
    //                     }
    //                 }
    //                 res.status(200).json(true);
    //             } else {
    //                 res.status(500).json(false);
    //             }
    //         } else {
    //             res.status(500).json('接收到空的数据体');
    //         }
    //     } catch (e) {
    //         res.status(500).json(e);
    //     }
    // }
    getWhereByParams(params) {
        return params.length > 0 ? (' where ' + params.join(' and ')) : '';
    }
    uploadFile_(file) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                if (!file) {
                    return null;
                }
                let fileName_ = FileUitl_1.default.newFileName(file.name);
                let path = path_1.default.resolve(this.config_.filesUrl, fileName_);
                if (!FileUitl_1.default.exist(this.config_.filesUrl)) {
                    yield fs_1.default.mkdirSync(this.config_.filesUrl);
                }
                let fileStr = FileUitl_1.default.fileSteamToString(file.data, false);
                yield this.writeScript(path, fileStr, fileName_);
                return fileName_;
            }
            catch (e) {
                console.error("ScriptStorager [uploadFile_] error", e);
            }
        });
    }
    writeScript(path, fileStr, fileName_) {
        return __awaiter(this, void 0, void 0, function* () {
            yield fs_1.default.writeFile(path, fileStr, (err) => __awaiter(this, void 0, void 0, function* () {
                if (err) {
                    console.error(path, "文件保存失败:", err);
                    return false;
                }
            }));
        });
    }
    file(name) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!name)
                return null;
            let buffer = yield FileUitl_1.default.readFile(path_1.default.resolve('./Images', name));
            if (buffer) {
                return buffer.toString('base64');
            }
            else {
                return null;
            }
        });
    }
    insertCrewInfo(crewData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let info = yield this.pool_.loadRows('select * from ship_member_registration_info');
                for (let i of crewData.data) {
                    let item = info.find(x => x.id_number == i.id_number);
                    if (item) {
                        continue;
                    }
                    else {
                        yield this.pool_.query('insert into ship_member_registration_info(relationship, nationality, id_type, id_number, name, all_fields, fromName,is_del) values(?,?,?,?,?,?,?,0)', [i.relationship, i.nationality, i.id_type, i.id_number, i.name, i.all_fields, crewData.fromName]);
                    }
                }
                return true;
            }
            catch (e) {
                console.error(e);
            }
        });
    }
    updateOrg(data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let ship_operation_code = data.ship_operation_code || '';
                let org_id = data.org_id || 0;
                let org_code = data.org_code || '';
                let org_name = data.org_name || '';
                let region = data.region || 0;
                let dataList = yield this.pool_.loadRows(`select * from ship_member_registration_info where ship_operation_code = '${ship_operation_code}'`);
                for (let item of dataList) {
                    this.pool_.query('update ship_member_registration_info set org_id=?,org_code=?,org_name=?,region=? where id=?', [org_id, org_code, org_name, region, item.id]);
                }
                return true;
            }
            catch (e) {
                console.error(e);
            }
        });
    }
}
exports.default = ShipMemberFileStorager;
//# sourceMappingURL=ShipMemberFileStorager.js.map
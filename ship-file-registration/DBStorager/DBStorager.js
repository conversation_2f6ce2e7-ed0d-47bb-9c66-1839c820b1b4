"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MySQL2Wrapper_1 = require("./MySQL2Wrapper");
const ShipFileStorager_1 = __importDefault(require("./ShipFileStorager"));
const ShipMemberFileStorager_1 = __importDefault(require("./ShipMemberFileStorager"));
const PriorityShipStorager_1 = __importDefault(require("./PriorityShipStorager"));
class DBStorager {
    constructor(_config) {
        let database = Object.assign({ multipleStatements: true }, _config.database || {
            host: _config.database.host,
            port: _config.database.port,
            database: _config.database.database,
            user: _config.database.user,
            password: _config.database.password,
        });
        this._pool = (0, MySQL2Wrapper_1.createPool)(database);
        this.connect();
    }
    connect() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                yield this._pool.getConnection();
                this.shipFileStorager_ = new ShipFileStorager_1.default(this._pool);
                this.shipMemberFileStorager_ = new ShipMemberFileStorager_1.default(this._pool);
                this.priorityShipStorager_ = new PriorityShipStorager_1.default(this._pool);
                console.log('数据库连接成功');
            }
            catch (e) {
                console.log('数据库连接失败', e);
            }
        });
    }
    get pool() {
        return this._pool;
    }
}
exports.default = DBStorager;
//# sourceMappingURL=DBStorager.js.map
"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const CommonUtil_1 = require("../Util/CommonUtil");
const moment_1 = __importDefault(require("moment"));
const FileUitl_1 = __importDefault(require("../Util/FileUitl"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
class ShipFileStorager {
    constructor(pool_) {
        this.pool_ = pool_;
        this.config_ = require('../config.json');
    }
    inoutcrew_(query) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let params = [];
                let sql = `select * from fishing_vessel_entry_and_exit_information  where ship_name =?`;
                params.push(query['ship_name']);
                let items = yield this.pool_.loadRows(sql, params);
                let items2 = [];
                let params2 = [];
                if (items && items.length > 0) {
                    let sql2 = `SELECT
            b.* 
            FROM
            fishing_vessel_entry_and_exit_information AS a
            LEFT JOIN crew_information_in_and_out_of_port AS b ON a.id = b.in_or_out_id 
            WHERE
            a.ship_name =? and b.id is not null `;
                    params2.push(query['ship_name']);
                    items2 = yield this.pool_.loadRows(sql2, params2);
                }
                return { items, items2 };
            }
            catch (e) {
                console.log(e);
                throw e;
            }
        });
    }
    inoutcrewHistory_(query) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let params = [];
                // let sql = `SELECT
                // a.*,b.duty,b.is_buy_insurance_zw
                // FROM
                // fishing_vessel_entry_and_exit_information AS a
                // right JOIN crew_information_in_and_out_of_port AS b ON a.id = b.in_or_out_id
                // WHERE
                // b.name =? and b.id_card_num=?
                // AND a.id IS NOT NULL`;
                let sql = `SELECT M.create_date as inout_time,I.ship_name, I.port_name, I.portreport_type ,I.number AS position_crew_num 
                               FROM crew_information_in_and_out_of_port M  
                               JOIN inout_info I 
                               ON M.in_or_out_id = I.source_id  
                               WHERE M.id_card_num = ? AND M.name = ? 
                               ORDER BY I.inout_time desc
                               limit 30`;
                params.push(query['id_card_num']);
                params.push(query['name']);
                let items = yield this.pool_.loadRows(sql, params);
                return { items };
            }
            catch (e) {
                console.log(e);
                throw e;
            }
        });
    }
    table(query) {
        return __awaiter(this, void 0, void 0, function* () {
            let totalWheres_ = [], pageIndex = 0, pageSize = 0, temp = '';
            let params = [];
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['shipName'])) {
                totalWheres_.push(`ship_name like ?`);
                params.push('%' + query['shipName'] + '%');
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['mmsi'])) {
                totalWheres_.push(`mmsi like ?`);
                params.push('%' + query['mmsi'] + '%');
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['shipType'])) {
                /*let type: string = ''
                for (const shipType of query['shipType']) {
                    type = shipType + ",";
                }*/
                totalWheres_.push(`ship_type in (?)`);
                params.push(query['shipType']);
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['home_port'])) {
                totalWheres_.push(`home_port like ?`);
                params.push('%' + query['home_port'] + '%');
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['isPriorityShip'])) {
                totalWheres_.push(`isPriorityShip = ?`);
                params.push(query['isPriorityShip']);
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['declaration_type'])) {
                totalWheres_.push(`declaration_type = ?`);
                params.push(query['declaration_type']);
            }
            else {
                totalWheres_.push(`declaration_type = 1`);
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['ship_company'])) {
                totalWheres_.push(`ship_company like ?`);
                params.push('%' + query['ship_company'] + '%');
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['gxpcs'])) {
                totalWheres_.push(`gxpcs like ?`);
                params.push('%' + query['gxpcs'] + '%');
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['operating_type'])) {
                totalWheres_.push(`operating_type like ?`);
                params.push('%' + query['operating_type'] + '%');
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['region'])) {
                totalWheres_.push(`region = '${query['region']}'`);
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['org_ids'])) {
                let org_ids = JSON.parse(query['org_ids']);
                let str = '\'' + org_ids.join('\',\'') + '\'';
                totalWheres_.push(`org_id in (${str})`);
            }
            pageIndex = query['pageIndex'];
            pageSize = query['pageSize'];
            let where = this.getWhereByParams(totalWheres_);
            let total = yield this.pool_.loadScalar(`select count(*) from ship_registration_info ${where}`, params);
            let sql = `select * from ship_registration_info ${where}`;
            sql += ` order by registrant_time desc `;
            if (pageSize > 0)
                sql += ` limit ${pageIndex * pageSize},${pageSize}`;
            let items = yield this.pool_.loadRows(sql, params);
            return { total, items };
        });
    }
    getShipMmsiList() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let sql = `SELECT mmsi FROM ship_registration_info WHERE mmsi != '' AND CHAR_LENGTH(mmsi) = 9 AND mmsi IS NOT null`;
                let items = yield this.pool_.loadRows(sql);
                return { total: items.length, items };
            }
            catch (e) {
                console.log('getShipMmsiList_ error', e);
                return { total: 0, items: [] };
            }
        });
    }
    updateShipMmsiByShipName(body) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let sql = 'update ship_registration_info set mmsi = ?,ship_en_name = ?,identification_code=? where ship_name = ?';
                yield this.pool_.query(sql, [
                    body.mmsi,
                    body.shipEnglishName,
                    body.shipId,
                    body.shipName
                ]);
                return true;
            }
            catch (e) {
                console.log('updateShipMmsiByShipName error', e);
                return false;
            }
        });
    }
    getShipByMmsi(mmsi) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.pool_.loadRow(`select * from ship_registration_info where mmsi = '${mmsi}'`);
        });
    }
    save(data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let code = data.ship_operation_code;
                if (!code) {
                    code = this.getUUID(16, 16);
                }
                if (data.image_url) {
                    for (let item of data.image_url) {
                        let fileName = item.fileName || '';
                        if (item.data) {
                            fileName = (yield this.uploadFile_(item)) || '';
                            delete item.data;
                        }
                        item.fileName = fileName;
                        item.url = '/api/filesUrl/shipFileRegistration/' + fileName;
                    }
                    data.image_url = JSON.stringify(data.image_url);
                }
                let ship_operation_code = code;
                let shipping_area = data.shipping_area;
                let ship_type = data.ship_type;
                let mmsi = data.mmsi;
                let home_port = data.home_port;
                let ship_name = data.ship_name;
                let ship_en_name = data.ship_en_name || '';
                let ship_owner = data.ship_owner;
                let ship_power = data.ship_power;
                let ship_length = data.ship_length || 0;
                let ship_width = data.ship_width || 0;
                let ship_height = data.ship_height || 0;
                let ship_material = data.ship_material;
                let ship_status = data.ship_status;
                let ton = data.ton || 0;
                let host_number_approved = data.host_number_approved || 0;
                let remarks = data.remarks || '';
                let ship_unit_code = data.ship_unit_code || '';
                let ship_unit_name = data.ship_unit_name || '';
                let ship_unit_address = data.ship_unit_address || '';
                let ship_owner_id = data.ship_owner_id || '';
                let ship_owner_name = data.ship_owner_name || '';
                let ship_owner_phone = data.ship_owner_phone || '';
                let registrant_unit_code = data.registrant_unit_code || '';
                let registrant_unit_name = data.registrant_unit_name || '';
                let registrant_user_id = data.registrant_user_id || '';
                let registrant_user_name = data.registrant_user_name || '';
                let registrant_time = data.registrant_time || (0, moment_1.default)(new Date()).format("YYYY-MM-DD HH:mm:ss");
                let ship_person_in_charge_name = data.ship_person_in_charge_name || '';
                let ship_person_in_charge_phone = data.ship_person_in_charge_phone || '';
                let ship_company = data.ship_company || '';
                let ship_use = data.ship_use || '';
                let ship_character = data.ship_character || '';
                let ship_source = data.ship_source || '';
                let operating_type = data.operating_type || '';
                let construction_manufacturer = data.construction_manufacturer || '';
                let permanent_berth = data.permanent_berth || '';
                let image_name = data.image_name || '';
                let image_url = data.image_url || '';
                let declaration_type = data.declaration_type || 1;
                let gxpcs = data.gxpcs || '';
                let org_id = data.org_id || 0;
                let org_code = data.org_code || '';
                let org_name = data.org_name || '';
                let region = data.region || 0;
                let from_name = data.from_name || 0;
                let all_fields = data.all_fields || null;
                let identification_code = data.identification_code || '';
                let type_code = this.getShipTypeCode(ship_type);
                if (declaration_type == 2) {
                    yield this.pool_.query('update ship_member_registration_info set ship_operation_code = ?, bound = ?, bound_ship_name = ?, is_del = ?, declaration_type = ? where bound_ship_name = ? and ship_operation_code = ?', [
                        "", 0, "", 1, 2, ship_name, ship_operation_code
                    ]);
                }
                yield this.pool_.query('insert into ship_registration_info (ship_operation_code, shipping_area, ship_type, mmsi, home_port, ship_name, ship_en_name, ship_owner , ship_power, ' +
                    'ship_length, ship_width, ship_height, ship_material, ship_status, ton, host_number_approved, remarks, ship_unit_code, ship_unit_name, ship_unit_address, ' +
                    'ship_owner_id, ship_owner_name, ship_owner_phone, registrant_unit_code, registrant_unit_name, registrant_user_id, registrant_user_name, registrant_time, ship_person_in_charge_name, ship_person_in_charge_phone, ship_company, ' +
                    'ship_use, ship_character, ship_source, operating_type, construction_manufacturer, permanent_berth, image_name, image_url,declaration_type,gxpcs,org_id,org_code,org_name,region,isPriorityShip,identification_code,fromName,all_fields) ' +
                    'values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)', [ship_operation_code, shipping_area, ship_type, mmsi, home_port, ship_name, ship_en_name, ship_owner, ship_power,
                    ship_length, ship_width, ship_height, ship_material, ship_status, ton, host_number_approved, remarks, ship_unit_code, ship_unit_name, ship_unit_address, ship_owner_id,
                    ship_owner_name, ship_owner_phone, registrant_unit_code, registrant_unit_name, registrant_user_id, registrant_user_name, registrant_time, ship_person_in_charge_name,
                    ship_person_in_charge_phone, ship_company, ship_use, ship_character, ship_source, operating_type, construction_manufacturer, permanent_berth, image_name, image_url,
                    declaration_type, gxpcs, org_id, org_code, org_name, region, 0, identification_code, from_name, all_fields]);
                return true;
            }
            catch (e) {
                console.error("[Save] Error:", e);
                return false;
            }
        });
    }
    update(data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                if (data.image_url) {
                    for (let item of data.image_url) {
                        let fileName = item.fileName || '';
                        if (item.data) {
                            fileName = (yield this.uploadFile_(item)) || '';
                            delete item.data;
                        }
                        item.fileName = fileName;
                        item.url = '/api/filesUrl/shipFileRegistration/' + fileName;
                    }
                    data.image_url = JSON.stringify(data.image_url);
                }
                let id = data.id;
                let ship_operation_code = data.ship_operation_code;
                let shipping_area = data.shipping_area;
                let ship_type = data.ship_type;
                let mmsi = data.mmsi;
                let home_port = data.home_port;
                let ship_name = data.ship_name;
                let ship_en_name = data.ship_en_name;
                let ship_owner = data.ship_owner;
                let ship_power = data.ship_power;
                let ship_length = data.ship_length || 0;
                let ship_width = data.ship_width || 0;
                let ship_height = data.ship_height || 0;
                let ship_material = data.ship_material;
                let ship_status = data.ship_status;
                let ton = data.ton || 0;
                let host_number_approved = data.host_number_approved || 0;
                let remarks = data.remarks || '';
                let ship_unit_code = data.ship_unit_code || '';
                let ship_unit_name = data.ship_unit_name || '';
                let ship_unit_address = data.ship_unit_address || '';
                let ship_owner_id = data.ship_owner_id || '';
                let ship_owner_name = data.ship_owner_name || '';
                let ship_owner_phone = data.ship_owner_phone || '';
                let registrant_unit_code = data.registrant_unit_code || '';
                let registrant_unit_name = data.registrant_unit_name || '';
                let registrant_user_id = data.registrant_user_id || '';
                let registrant_user_name = data.registrant_user_name || '';
                let registrant_time = data.registrant_time || null;
                let ship_person_in_charge_name = data.ship_person_in_charge_name || '';
                let ship_person_in_charge_phone = data.ship_person_in_charge_phone || '';
                let ship_company = data.ship_company || '';
                let ship_use = data.ship_use || '';
                let ship_character = data.ship_character || '';
                let ship_source = data.ship_source || '';
                let operating_type = data.operating_type || '';
                let construction_manufacturer = data.construction_manufacturer || '';
                let permanent_berth = data.permanent_berth || '';
                let image_name = data.image_name || '';
                let image_url = data.image_url || '';
                let declaration_type = data.declaration_type || '';
                let gxpcs = data.gxpcs || '';
                let org_id = data.org_id || 0;
                let org_code = data.org_code || '';
                let org_name = data.org_name || '';
                let region = data.region || 0;
                let identification_code = data.identification_code || '';
                let type_code = this.getShipTypeCode(ship_type);
                if (declaration_type == 2) {
                    yield this.pool_.query('update ship_member_registration_info set ship_operation_code = ?, bound = ?, bound_ship_name = ?, is_del = ?, declaration_type = ? where bound_ship_name = ? and ship_operation_code = ?', [
                        "", 0, "", 1, 2, ship_name, ship_operation_code
                    ]);
                }
                yield this.pool_.query('update ship_registration_info set ship_operation_code=?, shipping_area=?, ship_type=?, mmsi=?, home_port=?, ship_name=?, ship_en_name=?, ship_owner=?, ship_power=?,' +
                    ' ship_length=?, ship_width=?, ship_height=?, ship_material=?, ship_status=?, ton=?, host_number_approved=?, remarks=?, ship_unit_code=?, ship_unit_name=?, ship_unit_address=?,' +
                    ' ship_owner_id=?, ship_owner_name=?, ship_owner_phone=?, registrant_unit_code=?, registrant_unit_name=?, registrant_user_id=?, registrant_user_name=?, registrant_time=?, ship_person_in_charge_name=?,' +
                    ' ship_person_in_charge_phone=?, ship_company=?, ship_use=?, ship_character=?, ship_source=?, operating_type=?, construction_manufacturer=?, permanent_berth=?, image_name=?, image_url=?, ' +
                    ' declaration_type=?,gxpcs=?,org_id=?,org_code=?,org_name=?,region=?,identification_code=? where id =? ', [ship_operation_code, shipping_area, ship_type, mmsi, home_port, ship_name, ship_en_name, ship_owner, ship_power, ship_length, ship_width, ship_height, ship_material, ship_status, ton,
                    host_number_approved, remarks, ship_unit_code, ship_unit_name, ship_unit_address, ship_owner_id, ship_owner_name, ship_owner_phone, registrant_unit_code, registrant_unit_name,
                    registrant_user_id, registrant_user_name, registrant_time, ship_person_in_charge_name, ship_person_in_charge_phone, ship_company, ship_use, ship_character, ship_source, operating_type,
                    construction_manufacturer, permanent_berth, image_name, image_url, declaration_type, gxpcs, org_id, org_code, org_name, region, identification_code, id]);
                return true;
            }
            catch (e) {
                console.error("[Update] Error:", e);
                return false;
            }
        });
    }
    delete(data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let ships = data;
                for (let item of ships) {
                    let id = item.id;
                    let ship_operation_code = item.ship_operation_code;
                    yield this.pool_.query('delete from ship_registration_info where id = ?', [id]);
                    if (ship_operation_code) {
                        yield this.pool_.query('update ship_member_registration_info set ship_operation_code=?, bound=?, bound_ship_name=? where ship_operation_code=?', ['', 0, '', ship_operation_code]);
                    }
                }
                return true;
            }
            catch (e) {
                console.error("[Delete] Error:", e);
                return false;
            }
        });
    }
    getWhereByParams(params) {
        return params.length > 0 ? (' where ' + params.join(' and ')) : '';
    }
    getUUID(len, radix) {
        var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
        var uuid = [];
        var i;
        radix = radix || chars.length;
        if (len) {
            for (i = 0; i < len; i++)
                uuid[i] = chars[0 | Math.random() * radix];
        }
        else {
            var r;
            uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
            uuid[14] = '4';
            for (i = 0; i < 36; i++) {
                if (!uuid[i]) {
                    r = 0 | Math.random() * 16;
                    uuid[i] = chars[(i === 19) ? (r & 0x3) | 0x8 : r];
                }
            }
        }
        return uuid.join('');
    }
    getShipTypeCode(shipType) {
        let type_code = 0;
        switch (true) {
            case (shipType === '公务船'):
                type_code = 905;
                break;
            case (shipType == '游船'):
                type_code = 105;
                break;
            case (shipType == '渡船'):
                type_code = 104;
                break;
            case (shipType == '渔船'):
                type_code = 9001;
                break;
            case (shipType == '高速船'):
                type_code = 106;
                break;
            case (shipType == '危化品运输船'):
                type_code = 302;
                break;
            case (shipType == '普通运输船'):
                type_code = 200;
                break;
            case (shipType == '工程作业船'):
                type_code = 400;
                break;
            case (shipType === '其他'):
                type_code = 0;
                break;
        }
        return type_code;
    }
    insertShipInfo(fileInfo) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let info = yield this.pool_.loadRows('select * from ship_registration_info');
                for (let i of fileInfo.data) {
                    let item = info.find(x => x.mmsi == i.mmsi);
                    if (item) {
                        continue;
                    }
                    else {
                        yield this.pool_.query('insert into ship_registration_info(ship_name, mmsi, ship_type, home_port, ship_length,ton, ship_material, ship_owner_name, all_fields, fromName) values(?,?,?,?,?,?,?,?,?,?)', [i.ship_name, i.mmsi, i.ship_type, i.home_port, i.ship_length, i.ton, i.ship_material, i.ship_owner_name, i.all_fields, fileInfo.fromName]);
                    }
                }
                return true;
            }
            catch (e) {
                console.error(e);
            }
        });
    }
    shipFileUpload(item) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                if (item.mmsi) {
                    let sql = 'select * from ship_registration_info where mmsi = ? and ship_name = ?';
                    let row = yield this.pool_.loadRow(sql, [item.mmsi, item.ship_name]);
                    if (row && row.id) {
                        yield this.update(item);
                    }
                    else {
                        yield this.save(item);
                    }
                }
                else {
                    return;
                }
            }
            catch (e) {
                console.error(e);
            }
        });
    }
    getShipInfo(fields, where, params) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.pool_.loadRows(`select ${fields} from ship_registration_info ${where}`, params);
        });
    }
    uploadFile_(file) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                if (!file) {
                    return null;
                }
                let fileName_ = FileUitl_1.default.newFileName(file.name);
                let path = path_1.default.resolve(this.config_.filesUrl, fileName_);
                if (!FileUitl_1.default.exist(this.config_.filesUrl)) {
                    yield fs_1.default.mkdirSync(this.config_.filesUrl);
                }
                let fileStr = FileUitl_1.default.fileSteamToString(file.data, false);
                yield this.writeScript(path, fileStr, fileName_);
                return fileName_;
            }
            catch (e) {
                console.error("ScriptStorager [uploadFile_] error", e);
            }
        });
    }
    writeScript(path, fileStr, fileName_) {
        return __awaiter(this, void 0, void 0, function* () {
            yield fs_1.default.writeFile(path, fileStr, (err) => __awaiter(this, void 0, void 0, function* () {
                if (err) {
                    console.error(path, "文件保存失败:", err);
                    return false;
                }
            }));
        });
    }
    test() {
        return __awaiter(this, void 0, void 0, function* () {
            let datas = yield this.pool_.loadRows(`select * from ship_registration_info where image_url != ''`);
            for (let item of datas) {
                let fileStr = FileUitl_1.default.fileSteamToString(item.image_url, false);
                let fileName = this.getUUID(16, 16);
                let path = path_1.default.resolve(this.config_.filesUrl, fileName);
                yield this.writeScript(path, fileStr);
                let suffix = item.image_name.split('.')[1];
                let image_url = JSON.stringify([
                    {
                        "del": false,
                        "name": item.image_name,
                        "size": 150,
                        "src": "",
                        "params": [],
                        "fileName": fileName + '.' + suffix,
                        "url": '/' + fileName + '.' + suffix
                    }
                ]);
                let datas = yield this.pool_.loadRow(`update ship_registration_info set image_url = ? where id = ?`, [image_url, item.id]);
            }
        });
    }
    updatePartField(fields, where, params) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                yield this.pool_.query(`update ship_registration_info ${fields} ${where}`, params);
                return true;
            }
            catch (e) {
                console.error("[Update] Error:", e);
                return false;
            }
        });
    }
    updateLog(params) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let res = yield this.pool_.query(`
                INSERT INTO update_log (
                   create_time,
                   update_field,
                   old_field,
                   ship_operation_code,
                   mmsi
                )
                values
                (
                    ?,
                    ?,
                    ?,
                    ?,
                    ?
                );
        `, params);
                return true;
            }
            catch (e) {
                console.error("[Update] Error:", e);
                return false;
            }
        });
    }
}
exports.default = ShipFileStorager;
//# sourceMappingURL=ShipFileStorager.js.map
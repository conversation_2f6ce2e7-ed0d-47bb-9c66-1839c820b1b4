"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createPool = exports.MySQL2PromisePool = void 0;
const promise_1 = __importDefault(require("mysql2/promise"));
class MySQL2PromisePool {
    constructor(config) {
        this.pool = promise_1.default.createPool({
            host: config.host,
            port: config.port || 3306,
            user: config.user,
            password: config.password,
            database: config.database,
            waitForConnections: true,
            connectionLimit: 10,
            queueLimit: 0,
            multipleStatements: config.multipleStatements || false
        });
    }
    getConnection() {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.pool.getConnection();
        });
    }
    loadRows(sql, params) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const [rows] = yield this.pool.execute(sql, params);
                return rows;
            }
            catch (error) {
                console.error('loadRows error:', error);
                throw error;
            }
        });
    }
    loadRow(sql, params) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const [rows] = yield this.pool.execute(sql, params);
                const rowArray = rows;
                return rowArray.length > 0 ? rowArray[0] : null;
            }
            catch (error) {
                console.error('loadRow error:', error);
                throw error;
            }
        });
    }
    loadScalar(sql, params) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const [rows] = yield this.pool.execute(sql, params);
                const rowArray = rows;
                if (rowArray.length > 0) {
                    const firstRow = rowArray[0];
                    const firstKey = Object.keys(firstRow)[0];
                    return firstRow[firstKey];
                }
                return null;
            }
            catch (error) {
                console.error('loadScalar error:', error);
                throw error;
            }
        });
    }
    query(sql, params) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const [result] = yield this.pool.execute(sql, params);
                return result;
            }
            catch (error) {
                console.error('query error:', error);
                throw error;
            }
        });
    }
    transaction(sql, params) {
        return __awaiter(this, void 0, void 0, function* () {
            const connection = yield this.pool.getConnection();
            try {
                yield connection.beginTransaction();
                // 分割多个SQL语句
                const sqlStatements = sql.split(';').filter(s => s.trim().length > 0);
                let paramIndex = 0;
                let results = [];
                for (const statement of sqlStatements) {
                    const trimmedStatement = statement.trim();
                    if (trimmedStatement.length === 0)
                        continue;
                    // 计算当前语句需要的参数数量
                    const paramCount = (trimmedStatement.match(/\?/g) || []).length;
                    const currentParams = (params === null || params === void 0 ? void 0 : params.slice(paramIndex, paramIndex + paramCount)) || [];
                    paramIndex += paramCount;
                    const [result] = yield connection.execute(trimmedStatement, currentParams);
                    results.push(result);
                }
                yield connection.commit();
                return results;
            }
            catch (error) {
                yield connection.rollback();
                console.error('transaction error:', error);
                throw error;
            }
            finally {
                connection.release();
            }
        });
    }
}
exports.MySQL2PromisePool = MySQL2PromisePool;
function createPool(config) {
    return new MySQL2PromisePool(config);
}
exports.createPool = createPool;
//# sourceMappingURL=MySQL2Wrapper.js.map
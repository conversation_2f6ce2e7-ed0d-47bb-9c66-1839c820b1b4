"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const CommonUtil_1 = require("../Util/CommonUtil");
class PriorityShipStorager {
    constructor(pool_) {
        this.pool_ = pool_;
    }
    table(query) {
        return __awaiter(this, void 0, void 0, function* () {
            let totalWheres_ = [], pageIndex = 0, pageSize = 0, temp = '';
            let org_ids = query.org_ids;
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['shipName'])) {
                totalWheres_.push(`name like '%${query['shipName']}%'`);
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['mmsi'])) {
                totalWheres_.push(`mmsi = ${query['mmsi']}`);
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['cause'])) {
                totalWheres_.push(`cause like '%${query['cause']}%'`);
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['remark'])) {
                totalWheres_.push(`remark like '%${query['remark']}%'`);
            }
            if (!(0, CommonUtil_1.isUndefined_Empty)(query['keynoteTypes'])) {
                let str = '\'' + query['keynoteTypes'].join('\',\'') + '\'';
                totalWheres_.push(`keynoteType in (${str})`);
            }
            if (org_ids) {
                let str = '\'' + JSON.parse(org_ids).join('\',\'') + '\'';
                totalWheres_.push(` org_id in (${str})`);
            }
            pageIndex = query['pageIndex'];
            pageSize = query['pageSize'];
            let where = this.getWhereByParams(totalWheres_);
            let total = yield this.pool_.loadScalar(`select count(*) from priorityship ${where}`);
            let sql = `select * from priorityship ${where}`;
            if (pageSize > 0)
                sql += ` limit ${pageIndex * pageSize},${pageSize}`;
            let items = yield this.pool_.loadRows(sql);
            return { total, items };
        });
    }
    add(query) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let result = yield this.pool_.query('insert into priorityship(name,mmsi,homePort,administrator,phone,address,controlTime,cause,currentSituation,remark,copInCharge,org_id,org_name,region,org_code,keynoteType,shipNo,ship_company,ship_type) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)', [query.name, query.mmsi, query.homePort, query.administrator, query.phone, query.address, query.controlTime, query.cause, query.currentSituation, query.remark, query.copInCharge, query.org_id, query.org_name, query.region, query.org_code, query.keynoteType, query.shipNo, query.ship_company, query.ship_type]);
                let update = yield this.pool_.query('update ship_registration_info set isPriorityShip=1 where mmsi=?', [query.mmsi]);
                return true;
            }
            catch (e) {
                console.log(e);
                return false;
            }
        });
    }
    update(query) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let result = yield this.pool_.query('update priorityship set name=?, homePort=?, administrator=?, phone=?, address=?, controlTime=?, cause=?, currentSituation=?, remark=?, copInCharge=?, org_id=?,org_name=?,region=?,org_code=?,keynoteType=?,shipNo=?,ship_company=?,ship_type=? where id=?', [query.name, query.homePort, query.administrator, query.phone, query.address, query.controlTime, query.cause, query.currentSituation, query.remark, query.copInCharge, query.org_id, query.org_name, query.region, query.org_code, query.keynoteType, query.shipNo, query.ship_company, query.ship_type, query.id]);
                return true;
            }
            catch (e) {
                console.log(e);
                return false;
            }
        });
    }
    delete(query) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let del = yield this.pool_.query('delete from priorityship where id=?', [query.id]);
                let update = yield this.pool_.query('update ship_registration_info set isPriorityShip=0 where mmsi=?', [query.mmsi]);
                return true;
            }
            catch (e) {
                console.log(e);
                return false;
            }
        });
    }
    getWhereByParams(params) {
        return params.length > 0 ? (' where ' + params.join(' and ')) : '';
    }
}
exports.default = PriorityShipStorager;
//# sourceMappingURL=PriorityShipStorager.js.map
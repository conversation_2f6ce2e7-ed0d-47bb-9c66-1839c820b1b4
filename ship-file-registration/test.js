"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const MySQL2Wrapper_1 = require("./DBStorager/MySQL2Wrapper");
const pool = (0, MySQL2Wrapper_1.createPool)({
    host: "*************",
    database: "tzhf_zhhfgk",
    user: "tzhf",
    password: "tzhf123"
});
function run() {
    return __awaiter(this, void 0, void 0, function* () {
        //按船舶名称查询每条船舶最新的进出港数据（进出港数据mmsi非必须，所以用名称查询）
        let result = yield pool.loadRows("select d.* from inout_info d join(select ship_name, max(inout_time) as max_inout_time from inout_info group by ship_name) latest on d.ship_name = latest.ship_name and d.inout_time = latest.max_inout_time");
        for (let item of result) {
            // select crew.`name`,crew.in_or_out_id ,ino.ship_name,ino.source_id from crew_information_in_and_out_of_port crew
            //进出港船的信息
            // let inout_ship_mumber = await pool.loadRows('select * from crew_information_in_and_out_of_port where ')
            let crewInAndOutData = yield pool.loadRows("select * from crew_information_in_and_out_of_port where in_or_out_id = ? order by create_date desc", [item.source_id]);
            if (crewInAndOutData && crewInAndOutData.length > 0) {
                // let ship_member= await pool.loadRows('SELECT ship_member_registration_info.* FROM ship_member_registration_info WHERE ship_operation_code = ?',[item.ship_operation_code]);
                for (let item of crewInAndOutData) {
                    let history = yield pool.loadRow("select * from ship_crew_history where crew_id_number = ? and ship_name = ? and is_now = 1", [item.id_card_num, item.ship_name]);
                    let ship_member = yield pool.loadRow('select * from ship_member_registration_info where id_number = ?', item.id_card_num);
                    if (ship_member && history && ship_member.id_number == history.crew_id_number) {
                        yield pool.query('update ship_member_registration_info set is_del = 0 where is_del = 1');
                    }
                    else {
                        console.log('船员信息', ship_member.name, ship_member.id_number, ship_member.is_del);
                    }
                }
            }
        }
    });
}
//# sourceMappingURL=test.js.map
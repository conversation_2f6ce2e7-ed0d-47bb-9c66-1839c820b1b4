"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
class ShipMemberFileController {
    constructor(dbStorage_) {
        this.dbStorage_ = dbStorage_;
    }
    table(query) {
        return __awaiter(this, void 0, void 0, function* () {
            // @ts-ignore
            return yield this.dbStorage_.shipMemberFileStorager_.table(query);
        });
    }
    getByIdNumber(id_number) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipMemberFileStorager_.getByIdNumber(id_number);
        });
    }
    save(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipMemberFileStorager_.save(data);
        });
    }
    update(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipMemberFileStorager_.update(data);
        });
    }
    delete(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipMemberFileStorager_.delete(data);
        });
    }
    restore_(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipMemberFileStorager_.restore(data);
        });
    }
    shipMemberhistory(query) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipMemberFileStorager_.shipMemberhistory(query);
        });
    }
    addShipMemberTable(query) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipMemberFileStorager_.addShipMemberTable(query);
        });
    }
    addShipMember(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipMemberFileStorager_.addShipMember(data);
        });
    }
    addShipMemberHistory(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipMemberFileStorager_.addShipMemberHistory(data);
        });
    }
    deleteShipMember(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipMemberFileStorager_.deleteShipMember(data);
        });
    }
    // async shipMemberFileUpload(data: any): Promise<boolean> {
    //     return await this.dbStorage_.shipMemberFileStorager_.shipMemberFileUpload(data);
    // }
    insertCrewInfo(crewData) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipMemberFileStorager_.insertCrewInfo(crewData);
        });
    }
    updateOrg(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipMemberFileStorager_.updateOrg(data);
        });
    }
}
exports.default = ShipMemberFileController;
//# sourceMappingURL=ShipMemberFileController.js.map
"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
class ShipFileController {
    constructor(dbStorage_) {
        this.dbStorage_ = dbStorage_;
    }
    inoutcrew_(query) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipFileStorager_.inoutcrew_(query);
        });
    }
    inoutcrewHistory_(query) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipFileStorager_.inoutcrewHistory_(query);
        });
    }
    table(query) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipFileStorager_.table(query);
        });
    }
    getShipMmsiList() {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipFileStorager_.getShipMmsiList();
        });
    }
    updateShipMmsiByShipName(body) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipFileStorager_.updateShipMmsiByShipName(body);
        });
    }
    getShipByMmsi(mmsi) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipFileStorager_.getShipByMmsi(mmsi);
        });
    }
    save(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipFileStorager_.save(data);
        });
    }
    update(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipFileStorager_.update(data);
        });
    }
    delete(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipFileStorager_.delete(data);
        });
    }
    shipFileUpload(items) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipFileStorager_.shipFileUpload(items);
        });
    }
    insertShipInfo(fileInfo) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipFileStorager_.insertShipInfo(fileInfo);
        });
    }
    getShipInfo(fields, where, params) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipFileStorager_.getShipInfo(fields, where, params);
        });
    }
    updatePartField(fields, where, params) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipFileStorager_.updatePartField(fields, where, params);
        });
    }
    updateLog(params) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.shipFileStorager_.updateLog(params);
        });
    }
}
exports.default = ShipFileController;
//# sourceMappingURL=ShipFileController.js.map
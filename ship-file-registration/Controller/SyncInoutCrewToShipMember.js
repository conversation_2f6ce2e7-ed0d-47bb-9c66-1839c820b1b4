"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SyncInoutCrewToShipMember = void 0;
const moment_1 = __importDefault(require("moment"));
class SyncInoutCrewToShipMember {
    constructor(dbStorage_) {
        this.dbStorage_ = dbStorage_;
        this.shipFiles_ = new Map();
        /*setTimeout(this.initShipFiles.bind(this), 5000)
        setTimeout(this.syncCrew_.bind(this), 30000)*/
        //  void this.initShipFiles();
    }
    syncCrew_() {
        return __awaiter(this, void 0, void 0, function* () {
            let endTime = new Date();
            let startTime = new Date(endTime.getTime() - 24 * 3600 * 1000);
            let inoutIds = yield this.dbStorage_.pool.loadRows('select max(source_id) as source_id,ship_name,number,inout_time from inout_info where inout_time > ? and inout_time < ? and number is not null group by ship_name', [(0, moment_1.default)(startTime).format("YYYY-MM-DD HH:mm:ss"), (0, moment_1.default)(endTime).format("YYYY-MM-DD HH:mm:ss")]);
            console.log('start sync crew-----', inoutIds.length, (0, moment_1.default)(startTime).format("YYYY-MM-DD HH:mm:ss"), (0, moment_1.default)(endTime).format("YYYY-MM-DD HH:mm:ss"));
            for (let inout of inoutIds) {
                let inoutCrews = yield this.dbStorage_.pool.loadRows('select name as crew_name,id_card_num as crew_id_number,address ,duty as crew_relationship from crew_information_in_and_out_of_port where in_or_out_id =?', [inout.source_id]);
                if (inoutCrews.length !== inout.number) {
                    console.log('船员还没同步好？？？？？', inoutCrews.length, inout.number);
                    //查到的船员数量和上报的船员数量不一致，可能是船员同步没完成，先跳过
                    continue;
                }
                let ship = this.shipFiles_.get(inout.ship_name);
                if (ship) {
                    let fileCrews = yield this.dbStorage_.pool.loadRows('select * from ship_member_registration_info where ship_operation_code =? ', [ship.ship_operation_code]);
                    //判断离船
                    if (fileCrews && fileCrews.length) {
                        for (let fileCrew of fileCrews) {
                            let inoutCrew = inoutCrews.find((i) => i.crew_id_number == fileCrew.id_number);
                            if (!inoutCrew) {
                                //船员档案有，进出港报备无，船员应当离船
                                yield this.dbStorage_.shipMemberFileStorager_.deleteShipMember({
                                    data: {
                                        id_number: fileCrew.id_number,
                                        bound_ship_name: ship.ship_name,
                                        resignation_time: inout.inout_time
                                    }
                                });
                            }
                            else {
                                // console.log('都有都有-----', ship.ship_name, ship.ship_operation_code, inout.inout_time, inout.source_id);
                            }
                        }
                    }
                    //判断上船
                    let insertInoutCrews = [];
                    for (let inoutCrew of inoutCrews) {
                        if (fileCrews && fileCrews.length) {
                            let fileCrew = fileCrews.find((i) => i.id_number == inoutCrew.crew_id_number);
                            if (!fileCrew) { //进出港报备有，绑定船员档案无，需要增加船员或绑定船员
                                insertInoutCrews.push(inoutCrew);
                            }
                        }
                        else { //船舶没有绑定船员，全部新增/绑定船员
                            insertInoutCrews.push(inoutCrew);
                        }
                    }
                    //insertInoutCrews为需要上船的船员，先判断船员档案是否存在；
                    let ids = [];
                    for (let i of insertInoutCrews) {
                        let fileCrew = yield this.dbStorage_.pool.loadRow('select id,id_number from ship_member_registration_info where id_number=?', [i.crew_id_number]);
                        if (!fileCrew) {
                            // console.log('船员档案不存在------', i.crew_id_number);
                            let file = {
                                ship_operation_code: null,
                                relationship: i.crew_relationship,
                                nationality: '中国',
                                id_type: '身份证',
                                id_number: i.crew_id_number,
                                name: i.crew_name,
                                gender: null,
                                phone: null,
                                affiliated_unit: ship.ship_name,
                                census_address: i.address,
                                current_address: i.address,
                                org_id: ship.org_id,
                                org_code: ship.org_code,
                                org_name: ship.org_name,
                                region: ship.region
                            };
                            yield this.dbStorage_.shipMemberFileStorager_.save(file);
                            fileCrew = yield this.dbStorage_.pool.loadRow('select id,id_number from ship_member_registration_info where id_number=?', [i.crew_id_number]);
                            console.log('新增船员档案------', fileCrew.id, fileCrew.id_number);
                            fileCrew && ids.push(fileCrew.id);
                        }
                        else {
                            ids.push(fileCrew.id);
                        }
                    }
                    if (ids.length) {
                        console.log('更新船员关联关系--------', ids, ship.id);
                        yield this.dbStorage_.shipMemberFileStorager_.addShipMember({
                            id: ids,
                            time: inout.inout_time,
                            shipData: ship
                        });
                    }
                }
                else {
                    console.log('没有找到船舶档案---------------------------------------------------', inout.ship_name, inout.number, inout.source_id);
                }
            }
            //moment(new Date()).format("YYYY-MM-DD HH:mm:ss")
            setTimeout(this.syncCrew_.bind(this), 5 * 60 * 1000);
        });
    }
    initShipFiles() {
        return __awaiter(this, void 0, void 0, function* () {
            //  console.log('SyncInoutCrewToShipMember----更新船舶档案-----');
            try {
                let { total, items } = yield this.dbStorage_.shipFileStorager_.table({ pageIndex: 0, pageSize: 99999 });
                console.log('SyncInoutCrewToShipMember---', total, items.length);
                let temp = new Map();
                if (items.length) {
                    for (let item of items) {
                        temp.set(item.ship_name, item);
                    }
                    this.shipFiles_.clear();
                    this.shipFiles_ = temp;
                    console.log('SyncInoutCrewToShipMember------档案数量-------', this.shipFiles_.size, total);
                }
            }
            catch (e) {
                console.error('initShipFiles----错误', e);
            }
            setTimeout(this.initShipFiles.bind(this), 3600 * 1000);
            //  await this.syncCrew_('2024-01-02')
        });
    }
}
exports.SyncInoutCrewToShipMember = SyncInoutCrewToShipMember;
//# sourceMappingURL=SyncInoutCrewToShipMember.js.map
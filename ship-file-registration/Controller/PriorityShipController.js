"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
class PriorityShipController {
    constructor(dbStorage_) {
        this.dbStorage_ = dbStorage_;
    }
    table_(query) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.priorityShipStorager_.table(query);
        });
    }
    update_(query) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.priorityShipStorager_.update(query);
        });
    }
    add_(query) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.priorityShipStorager_.add(query);
        });
    }
    delete_(query) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.dbStorage_.priorityShipStorager_.delete(query);
        });
    }
}
exports.default = PriorityShipController;
//# sourceMappingURL=PriorityShipController.js.map
# 政务网系统快速使用指南

## 📋 系统概述

这是一个基于 Node.js + TypeScript 的海事监管政务网系统，包含多个微服务模块，用于船舶监管、无人机管控、预警处理等功能。

## 🏗️ 系统架构

```
政务网系统
├── 后端微服务 (Node.js/TypeScript)
│   ├── uav-web-service (无人机服务)
│   ├── ship-build-data (船舶建造数据)
│   ├── ship-file-registration (船舶档案登记)
│   ├── mqtt (MQTT消息服务)
│   ├── thys-service (通航要素服务)
│   └── 其他服务...
├── 前端模块 (Vue.js + TypeScript)
│   └── webgis/ (地图可视化模块)
└── 数据集
    ├── dataset/ (数据资源)
    └── desktop/ (桌面应用)
```

## 🚀 快速启动

### 已启动的服务

#### 1. MQTT 消息服务 ✅
```bash
# 状态：正在运行
# 端口：25112 (WebSocket)
# 功能：接收车辆和人脸预警事件，通过WebSocket推送
```

#### 2. 船舶建造数据服务 ✅
```bash
# 状态：正在运行  
# 端口：12998
# 功能：接收船舶建造报告数据
```

### 启动其他服务

#### 船舶档案登记服务
```bash
cd ship-file-registration
npm install
npm run prepack  # 编译TypeScript
node index.js
```

#### 无人机服务 (需要私有包权限)
```bash
cd uav-web-service
# 需要配置私有npm仓库访问权限
npm install
node index.js
```

## 📡 API 接口文档

### 1. 船舶建造数据服务 (端口: 12998)

#### POST /api/build/ShipBuildingReport
接收船舶建造报告数据

**请求格式:**
```json
{
  "appKey": "e4530440-9134-b100-1197-2081088a089a",
  "requestTime": 1234567890,
  "sign": "md5签名",
  "content": "JSON格式的船舶数据"
}
```

**签名算法:**
```javascript
// MD5(appKey + appSecret + requestTime).toLowerCase()
const sign = md5(appKey + appSecret + requestTime).toLowerCase();
```

**响应格式:**
```json
{
  "code": "00",
  "data": {
    "status": 200,
    "msg": "成功"
  }
}
```

### 2. 无人机服务 (端口: 23590)

#### GET /api/UAVOperation/aliveUAV
获取在线无人机列表

**查询参数:**
- `org_ids`: 组织ID数组 (JSON字符串)

**响应示例:**
```json
[
  {
    "id": 1,
    "name": "无人机001",
    "deptId": 16,
    "ltOrgCode": "331000000000",
    "ltOrgId": 21,
    "status": "online"
  }
]
```

#### GET /api/UAVOperation/getLiveStream
获取无人机直播流

**查询参数:**
- `sn`: 无人机序列号

### 3. 船舶档案登记服务

基于您选中的代码，该服务包含两个主要模块：

#### 船舶档案模块 (ShipFileRouter)
```typescript
// 服务初始化代码
const shipFileController: ShipFileController = new ShipFileController(dbStorager_);
const shipFileRouter: ShipFileRouter = new ShipFileRouter(shipFileController);
server_.BindingRouter(shipFileRouter.Router);
```

**主要功能:**
- 船舶基本信息管理
- 船舶证书管理
- 船舶所有权登记
- 船舶档案查询

**API接口 (推测):**
- `GET /api/shipFile/list` - 获取船舶档案列表
- `POST /api/shipFile/add` - 新增船舶档案
- `PUT /api/shipFile/update` - 更新船舶档案
- `DELETE /api/shipFile/delete` - 删除船舶档案

#### 船员档案模块 (ShipMemberRouter)
```typescript
// 服务初始化代码
const shipMemberFileController: ShipMemberFileController = new ShipMemberFileController(dbStorager_);
const shipMemberRouter: ShipMemberFileRouter = new ShipMemberFileRouter(shipMemberFileController);
server_.BindingRouter(shipMemberRouter.Router);
```

**主要功能:**
- 船员基本信息管理
- 船员证书管理
- 船员履历记录
- 船员与船舶关联

**API接口 (推测):**
- `GET /api/shipMember/list` - 获取船员档案列表
- `POST /api/shipMember/add` - 新增船员档案
- `PUT /api/shipMember/update` - 更新船员档案
- `GET /api/shipMember/byShip/:shipId` - 根据船舶获取船员

## 🔐 身份验证

### 有验证的服务
- **thys-service**: 需要用户token验证
- **user-login-transfer**: 多种登录方式验证

### 无验证的服务
- **uav-web-service**: ⚠️ 完全无验证
- **ship-build-data**: API密钥验证
- **mqtt**: WebSocket服务

### 验证机制
```javascript
// 请求头格式
headers: {
  'x-lt-user-token': '用户ID',
  'x-lt-user-org': '组织ID',
  'x-lt-internal-use': 'litong'
}
```

## 🛠️ 开发环境配置

### 环境要求
- Node.js 16+
- TypeScript 4+
- MySQL (部分服务需要)
- Redis (部分服务需要)

### 依赖安装
```bash
# 公共依赖
npm install express body-parser moment axios

# 开发依赖
npm install -D typescript @types/node @types/express ts-node
```

### 编译运行
```bash
# 编译TypeScript
npx tsc

# 或使用ts-node直接运行
npx ts-node index.ts
```

## 📁 项目结构说明

### 典型服务结构
```
service-name/
├── Controller/          # 控制器层
├── DBStorager/         # 数据访问层  
├── Router/             # 路由层
├── Model/              # 数据模型
├── Util/               # 工具类
├── config.json         # 配置文件
├── index.ts           # 入口文件
├── package.json       # 依赖配置
└── tsconfig.json      # TS配置
```

### 前端模块结构
```
webgis/
├── alarm-judge/        # 预警判断模块
│   ├── main.ts        # 模块入口
│   ├── AlarmCapturePreview.vue  # 预警抓拍预览
│   └── *.vue          # 其他组件
├── ship-build/         # 船舶建造模块
├── navenv/            # 导航环境模块
├── maps.*/            # 地图相关模块
├── ui.main/           # UI主框架 (缺失)
└── *.vue              # Vue组件
```

### 前端模块启动 (需要litong框架)

**⚠️ 注意**: 前端模块依赖 `litong` 核心框架，该框架目前缺失。

#### 缺失的核心依赖
```typescript
// 这些模块当前不可用
import { Plugins } from "litong/plugins/Plugins";
import { VueView } from "litong/ui/VueView";
import { VueHelper } from "litong/VueHelper";
import { UserSession } from "litong/user/UserSession";
```

#### 前端模块功能说明

**alarm-judge (预警判断)**
- 功能：处理车辆和人脸预警事件
- 组件：AlarmCapturePreview.vue (预警抓拍预览)
- 依赖：需要MQTT WebSocket服务 (端口25112)

**ship-build (船舶建造)**
- 功能：船舶建造进度监控
- 依赖：ship-build-data服务 (端口12998)

**navenv (导航环境)**
- 功能：海事导航环境监控
- 依赖：地图服务和导航数据

#### 临时解决方案
如需运行前端模块，可以：
1. 搭建独立的Vue开发环境
2. 重构去除litong依赖
3. 或联系原开发团队获取完整框架

## 🧪 测试和调试

### API 测试示例

#### 1. 测试船舶建造数据接口
```bash
# 生成签名
appKey="e4530440-9134-b100-1197-2081088a089a"
appSecret="a42854ae-9134-b100-1198-9c9c80a18f5a"
requestTime=$(date +%s)
sign=$(echo -n "${appKey}${appSecret}${requestTime}" | md5sum | cut -d' ' -f1)

# 发送请求
curl -X POST http://localhost:12998/api/build/ShipBuildingReport \
  -H "Content-Type: application/json" \
  -d "{
    \"appKey\": \"${appKey}\",
    \"requestTime\": ${requestTime},
    \"sign\": \"${sign}\",
    \"content\": \"{\\\"shipName\\\":\\\"测试船舶\\\",\\\"buildStatus\\\":\\\"在建\\\"}\"
  }"
```

#### 2. 测试无人机接口
```bash
# 获取在线无人机
curl "http://localhost:23590/api/UAVOperation/aliveUAV"

# 获取组织树
curl "http://localhost:23590/api/UAVOperation/treeDetail"

# 获取SSO Token
curl "http://localhost:23590/api/UAVOperation/sso"
```

#### 3. 测试WebSocket连接
```javascript
// 连接MQTT WebSocket服务
const ws = new WebSocket('ws://localhost:25112');
ws.onopen = () => console.log('WebSocket连接成功');
ws.onmessage = (event) => console.log('收到消息:', event.data);
```

### 服务状态检查
```bash
# 检查所有服务端口
netstat -tulpn | grep -E "(12998|23590|25112)"

# 检查进程状态
ps aux | grep -E "(node|ts-node)" | grep -v grep

# 检查服务响应
curl -I http://localhost:12998/api/build/ShipBuildingReport
curl -I http://localhost:23590/api/UAVOperation/aliveUAV
```

## 🔧 常见问题

### 1. 私有包访问问题
```bash
# 错误：Unable to authenticate @ltaq packages
# 解决：需要配置私有npm仓库权限
npm login --registry=http://dev.ltaq.com:4873/

# 或者跳过私有包，手动安装公共依赖
npm install express body-parser moment axios --registry https://registry.npmmirror.com
```

### 2. 端口冲突
```bash
# 检查端口占用
lsof -i :端口号

# 杀死占用进程
kill -9 PID

# 修改配置文件中的端口
vim config.json
```

### 3. TypeScript编译问题
```bash
# 清理编译缓存
rm -rf node_modules/.cache
rm -rf dist/

# 重新编译
npx tsc --build --clean
npx tsc
```

### 4. 数据库连接问题
```bash
# 检查MySQL服务
brew services list | grep mysql
# 或
systemctl status mysql

# 测试数据库连接
mysql -h localhost -u username -p database_name
```

## 📞 技术支持

### 服务端口映射
- 12998: ship-build-data (船舶建造数据)
- 23590: uav-web-service (无人机服务)  
- 25112: mqtt (WebSocket消息服务)

### 日志查看
```bash
# 查看服务日志
tail -f logs/service.log

# 查看错误日志  
tail -f logs/error.log
```

### 健康检查
```bash
# 检查服务状态
curl http://localhost:端口/health

# 检查API可用性
curl http://localhost:端口/api/接口路径
```

## 📋 快速参考表

### 服务状态一览
| 服务名称 | 端口 | 状态 | 验证方式 | 主要功能 |
|---------|------|------|----------|----------|
| mqtt | 25112 | ✅ 运行中 | 无 | WebSocket消息推送 |
| ship-build-data | 12998 | ✅ 运行中 | API密钥 | 船舶建造数据接收 |
| uav-web-service | 23590 | ❌ 需私有包 | 无 | 无人机管控 |
| ship-file-registration | - | ⚠️ 可启动 | 未知 | 船舶档案登记 |
| thys-service | - | ❌ 需私有包 | 用户Token | 通航要素服务 |

### 常用命令速查
```bash
# 启动服务
cd service-name && npm install && npx ts-node index.ts

# 检查端口
lsof -i :端口号

# 测试API
curl http://localhost:端口/api/接口路径

# 查看日志
tail -f logs/*.log

# 编译TypeScript
npx tsc

# 安装依赖 (跳过私有包)
npm install --registry https://registry.npmmirror.com
```

### 配置文件位置
```
service-name/
├── config.json        # 服务配置
├── package.json       # 依赖配置
└── tsconfig.json      # TS编译配置
```

### 重要端口映射
- **12998**: 船舶建造数据服务
- **23590**: 无人机Web服务
- **25112**: MQTT WebSocket服务
- **3306**: MySQL数据库 (如果使用)
- **6379**: Redis缓存 (如果使用)

---

**注意**: 部分服务需要私有npm包权限和数据库配置才能完整运行。建议先从已启动的服务开始熟悉系统功能。

**推荐启动顺序**:
1. ✅ mqtt (已启动)
2. ✅ ship-build-data (已启动)
3. 🔄 ship-file-registration (可尝试启动)
4. ⚠️ 其他服务 (需要解决依赖问题)

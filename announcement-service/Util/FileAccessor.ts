import * as path from 'path';
import * as fs from "fs";
import {FileUtil} from "./FileUtil";
import {ConfigUtils} from "./ConfigUtils";

function findStorePath() {
    if (ConfigUtils.cacheFilePath) {
        return ConfigUtils.cacheFilePath;
    } else {
        return path.join(__dirname, "..", "db_files");
    }
}

export class FileAccessor {
    private readonly RootPath = findStorePath(); //数据存储根路径，如：D:\ltxy\data\event-alarm或者 ..\db_files
    private readonly _fileName: string;

    constructor(dType: string) {
        this._fileName = path.join(this.RootPath, `${dType}.json`);
    }

    public save(data: any) {
        this.checkDir_();
        fs.writeFileSync(this._fileName, JSON.stringify(data,undefined," "), {
            encoding: 'utf-8',
            flag: 'w',
        });
    }

    public load(): any | undefined {
        this.checkDir_();
        if (fs.existsSync(this._fileName)) {
            let fc = fs.readFileSync(this._fileName, {encoding: 'utf-8'});
            if (fc) {
                return JSON.parse(fc);
            }
        }
    }

    /**
     * 判断文件是否存在，不存在则创建之。
     * @private
     */
    private checkDir_() {
        FileUtil.tryToMakeDir(this.RootPath);
    }
}
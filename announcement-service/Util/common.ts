import * as fs from "fs";

export function parseRange(ref: string): TableRange {
    let rangeEnd = ref.split(':')[1];
    let colStr = rangeEnd.match(/^[a-z|A-Z]+/gi);
    let rowStr = rangeEnd.match(/\d+$/gi);
    if (colStr && colStr[0] && rowStr && rowStr[0]) {
        let colnum = excelColStrToNum(colStr[0], colStr[0].length)
        return {columns: colnum, rows: Number(rowStr[0])};
    }else {
        return {columns:0, rows: 0};
    }

}

export function createFolder(path: string) {
    try {
        if (!fs.existsSync(path)) {
            fs.mkdirSync(path);
        }
        fs.accessSync(path);
    } catch (e) {
        fs.mkdirSync(path);
    }
}

export function excelColStrToNum(colStr: string, length: number) {
    let num: number = 0;
    let result: number = 0;
    for (let i = 0; i < length; i++) {
        let ch = colStr.charAt(length - i - 1);
        num = (ch.charCodeAt(0) - 'A'.charCodeAt(0) + 1) as number;
        num *= Math.pow(26, i);
        result += num;
    }
    return result;
}

export function excelColIndexToStr(columnIndex: number) {
    if (columnIndex <= 0) {
        return '';
    }
    let columnStr = "";
    columnIndex--;
    do {
        if (columnStr.length > 0) {
            columnIndex--;
        }
        columnStr = String.fromCharCode(columnIndex % 26 + 'A'.charCodeAt(0)) + columnStr;
        columnIndex = (columnIndex - columnIndex % 26) / 26;
    } while (columnIndex > 0);
    return columnStr;
}

interface TableRange {
    rows: number;
    columns: number;
}



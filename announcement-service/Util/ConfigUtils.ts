class _ConfigUtils {
    private config_: Config;

    constructor() {
        this.config_ = require("../config.json");
    }

    get port(): number {
        return this.config_.port;
    }

    get wsport(): number {
        return this.config_.wsport;
    }

    get database(): DataBase {
        return this.config_.database;
    }



    get userApi(): string {
        return this.config_.userApi;
    }

    get path(): string {
        return this.config_.path;
    }

    get cacheFilePath(): string {
        return this.config_.cacheFilePath;
    }
}

interface Config {
    port: number; //http服务端口
    wsport: number;//ws端口
    database: DataBase; //数据库。
    userApi: string;//用户服务
    path: string;//上传文件路径
    cacheFilePath: string; //缓存文件路径。
}

interface DataBase {
    host: string;
    port: string;
    name: string;
    uid: string;
    pwd: string;
}




export const ConfigUtils = new _ConfigUtils();
import bent from 'bent';
import {Request} from 'express';
import {ConfigUtils} from "./ConfigUtils";

class _UserUtil {
    public readonly TOKEN_CLIENT_KEY = 'x-lt-user-token';
    public readonly ORG_CLIENT_KEY = 'x-lt-user-org';
    public readonly INTERNAL_SPECIAL_KEY = 'x-lt-internal-use';

    private headers_: { [key: string]: string } = {};
    private allOrg: Map<number, OrganizationNode> = new Map<number, OrganizationNode>();

    constructor() {
        this.headers_[this.INTERNAL_SPECIAL_KEY] = 'litong';
        this.initWhole_();
    }

    private async initWhole_() {
        let get = bent(ConfigUtils.userApi, 'GET', 'json', 200);

        let data = await get('/org/tree/whole', {}, this.headers_);
        if (data) {
            this.allOrg.clear();
            this.flatTree(data as OrganizationNode[]).forEach(org => this.allOrg.set(org.id, org));
        }
    }

    public flatTree(tree?: OrganizationNode[]): Organization[] {
        let orgs: Organization[] = [];
        this.flatTree_(tree, orgs);
        return orgs;
    }

    private flatTree_(nodes: OrganizationNode[] | undefined, ret: Organization[]) {
        if (nodes) {
            for (let node of nodes) {
                ret.push(node);
            }
            for (let node of nodes) {
                this.flatTree_(node.children, ret);
            }
        }
    }

    public getUserId(req: Request): number | null {
        return parseInt(req.header(this.TOKEN_CLIENT_KEY) as string) || null;
    }

    public getOrgId(req: Request): number | null {
        return parseInt(req.header(this.ORG_CLIENT_KEY) as string) || null;
    }

    public async getDownwardOrgs(req: Request) {
        let orgId = parseInt(req.header(this.ORG_CLIENT_KEY) as string);
        let result: OrganizationNode[] = [];
        if (orgId) {
            let org = this.allOrg.get(orgId);
            if (org)
                return this.flatTree([org]);
        }
        return result;
    }

    public async getUserAccess(privCode: string, userId: number | null): Promise<UserAccessResult | null> {
        let post = bent(ConfigUtils.userApi, 'POST', 'json', 200);
        try {
            return (await post('/locale/useraccess', {user_id: userId, priv_code: privCode}, this.headers_)) as any;
        } catch (e:any) {
            console.error('获取UserAccess失败:', e && e.message || e.statusText || e);
        }
        return null;
    }

}

//用户访问范围查询参数
export interface UserAccesssParam {
    user_id: number;
    priv_code: string; //权限码
}

//用户访问范围信息
export interface UserAccessResult {
    user_id: number;
    isSupper: boolean; //是否是超级管理员
    access_user_ids?: number[];
    access_org_ids?: number[];
}

//组织机构
export class Organization {
    id!: number;
    name!: string;
    code?: string; //组织机构编码
    pid!: number; //父级机构ID
    order_no!: number;
    level!: number; //组织机构层级，该字段不在数据库中存储，只用作标注分级
    extension?: { [key: string]: any }; //扩展信息，存储在数据库中的json字符串
}

//组织机构树节点。
export class OrganizationNode extends Organization {
    children?: OrganizationNode[]

    constructor(org: Organization) {
        super();
        Object.assign(this, org);
    }
}

export const UserUtil = new _UserUtil();

import * as fs from "fs";
import * as path from "path";

export class FileUtil {
    public static removeDir(fPath: string) {
        if (fPath && fs.existsSync(fPath)) {
            let files: string[] = fs.readdirSync(fPath);
            if (files && files.length > 0) {
                for (let fn of files) {
                    let fullPath = path.join(fPath, fn);
                    let stat = fs.statSync(fullPath);
                    if (stat.isFile()) {
                        FileUtil.removeFile(fullPath);
                    } else if (stat.isDirectory()) {
                        FileUtil.removeDir(fullPath);
                    }
                }
            }
            fs.rmdirSync(fPath);
        }
    }

    public static removeFile(fName: string) {
        if (fs.existsSync(fName))
            fs.unlinkSync(fName);
    }

    public static renamePath(oldPath: string, newPath: string): boolean {
        if (fs.existsSync(oldPath) && newPath) {
            fs.renameSync(oldPath, newPath);
            return true;
        }
        return false;
    }

    public static tryToMakeDir(dir: string) {
        if (!fs.existsSync(dir)) {
            FileUtil.tryToMakeDir(path.dirname(dir));
            fs.mkdirSync(dir);
        }
    }

    public static existDir(dir: string): boolean {
        return fs.existsSync(dir);
    }
}

export interface AnnouncementModel {//公告
    id: string,//uuid
    createUserName: string,//发布人姓名，id,机构id,机构名
    createUserId: number,
    createOrgId: number,
    createOrgName: string,
    title: string,//100  公告标题
    content: string,//text  公告内容
    time: Date,
    receiveUsers: number[];
    timeoutTime: Date;
}


export interface FileModel {//文件
    id: string,//uuid
    createUserName: string,//发布人姓名，id,机构id,机构名
    createUserId: number,
    createOrgId: number,
    createOrgName: string,
    title: string,//100   文件标题
    file: string,//text   文件地址
    fileName: string,// 文件名称
    comment: string,//备注
    time: Date,
    receiveUsers: number[];
    timeoutTime: Date;
}

export interface TrainingModel {//培训
    id: string,//uuid
    createUserName: string,//发布人姓名，id,机构id,机构名
    createUserId: number,
    createOrgId: number,
    createOrgName: string,
    title: string,//100  培训标题
    content: string,//text  培训内容
    time: Date,
    receiveUsers: number[];
    timeoutTime: Date;
}

import {createPool, PromisePool} from "@ltaq/mysqlaa";

import moment from "moment";

import {AnnouncementModel} from "../model/AnnouncementModel";

export class DbConnect {
    private pool_: PromisePool;

    constructor(config: any) {
        this.pool_ = createPool({
            host: config.host,
            port: config.port,
            database: config.dataBase,
            user: config.user,
            password: config.password,
            connectionLimit: 10,
            multipleStatements: true
        });
        //zs_announcement  公告
        //zs_file  文件
        //zs_training   培训

    }

    public get pool(): PromisePool {
        return this.pool_;
    }


    public saveAnnouncement(announcement: AnnouncementModel) {
        return this.pool_.query(`insert into zs_announcement(id,createUserName,createUserId,createOrgName,createOrgId,title,content,receiveUsers,time,timeoutTime,deleted)
        values(?,?,?,?,?,?,?,?,?,?,?)`, [announcement.id, announcement.createUserName, announcement.createUserId, announcement.createOrgName, announcement.createOrgId,
            announcement.title, announcement.content, JSON.stringify(announcement.receiveUsers), announcement.time, announcement.timeoutTime, 0]);
    }

}

import {DbConnect} from "../db/DbConnect";
import {AnnouncementModel} from "../model/AnnouncementModel";
import moment from "moment";
import {WebSocketPublisher} from "../websocket/WebSocketPublisher";
import {ConfigUtils} from "../Util/ConfigUtils";
import {FileAccessor} from "../Util/FileAccessor";
import {MyLogger} from "../MyLogger";
import {GUID} from "../Util/GUID";

export default class AnnouncementHandler {
    private records_: Map<string, AnnouncementModel>;
    private changedRecords_: Set<string>;
    private cache_: AnnouncementCache;
    private changed_: boolean = false;


    constructor(private db_: DbConnect, private publisher: WebSocketPublisher) {
        this.records_ = new Map<string, AnnouncementModel>();
        this.changedRecords_ = new Set<string>();
        this.cache_ = new AnnouncementCache();
        void this.loadCache_();
        this.persistCacheData_();
        this.autoRecovery();
        void this.updateDbReceiveUsers_();
    }

    get records(): AnnouncementModel[] {
        return Array.from(this.records_.values());
    }

    private loadCache_() {
        let wraps = this.cache_.load();
        for (let one of wraps) {
            if (new Date(one.timeoutTime).getTime() > Date.now())
                this.records_.set(one.id, one)
        }
        MyLogger.log('加载公告缓存数据');
    }

    private persistCacheData_() {
        if (this.changed_) {
            this.changed_ = false;
            this.cache_.save(this.records);
        }
        setTimeout(this.persistCacheData_.bind(this), 10000)
    }


    public addRecord(announcement: AnnouncementModel) {

        this.records_.set(announcement.id, announcement);
        this.publisher.add('announcement', announcement);
        void this.db_.saveAnnouncement(announcement);
        this.changed_ = true;
    }

    public formatMsg(data: any): AnnouncementModel | undefined {
        let result: AnnouncementModel | undefined;
        try {
            result = {
                // id:data.id,
                id: GUID.newGUID(),
                createUserName: data.createUserName,
                createUserId: data.createUserId,
                createOrgId: data.createOrgId,
                createOrgName: data.createOrgName,
                title: data.title,
                content: data.content,
                time: new Date(data.time),
                receiveUsers: [],
                timeoutTime: new Date(data.timeoutTime)

            }
        } catch (e) {
            console.error('格式化公告消息错误', e)
        }

        return result;
    }

    public addReceiveUser(announcementId: string, userId: number) {
        let announcement = this.records_.get(announcementId);
        if (announcement) {
            announcement.receiveUsers.push(userId);
            this.changedRecords_.add(announcementId);
        }
        this.publisher.addReceiveUser(announcementId, userId);
    }

    public async delete(id: string) {
        this.records_.delete(id);
        this.changedRecords_.delete(id);
        this.publisher.clearById(id);
        let sql = `update zs_announcement set deleted=1 ,deleteTime= '${moment(new Date()).format('YYYY-MM-DD HH:mm:ss')}' where id=? `;
        return await this.db_.pool.query(sql, [id]);
    }

    private async updateDbReceiveUsers_() {//已读的用户更新可能比较频繁，10分钟同步一次数据库
        if (this.changedRecords_.size) {
            let arr = Array.from(this.changedRecords_);
            this.changedRecords_.clear();
            for (let id of arr) {
                let announcement = this.records_.get(id);
                if (announcement)
                    void await this.db_.pool.query(`update zs_announcement set receiveUsers=? where id =?`, [JSON.stringify(announcement.receiveUsers), announcement.id])
            }
        }
        setTimeout(this.updateDbReceiveUsers_.bind(this), 10 * 60 * 1000);
    }

    private autoRecovery() {
        let now = Date.now();
        let deleteKeys: string[] = [];
        for (let [k, wrap] of this.records_) {
            if (wrap.timeoutTime && (now - wrap.timeoutTime.getTime()) >= 0) {
                deleteKeys.push(wrap.id);
            }
        }
        for (const item of deleteKeys) {
            this.publisher.clearById(item);
            //this.db_.endAlarm_(item);
            this.records_.delete(item);
            this.changed_ = true;
        }
        setTimeout(this.autoRecovery.bind(this), 60000);
    }


}

class AnnouncementCache {
    private cache_: FileAccessor;

    constructor() {
        this.cache_ = new FileAccessor('announcement');
    }

    public load(): AnnouncementModel[] {
        let wraps: AnnouncementModel[] = [];
        try {
            wraps = this.cache_.load() || [];
        } catch (e: any) {
            MyLogger.error(`====0====加载公告数据失败:${e && e.message}`);
        }
        for (let wrap of wraps) {
            if (wrap) {
                try {
                    if (wrap.time) {
                        wrap.time = new Date(wrap.time);
                    }
                    if (wrap.timeoutTime) {
                        wrap.timeoutTime = new Date(wrap.timeoutTime);
                    }
                } catch (e: any) {
                    MyLogger.error(`转换公告缓存时间字段错误:${e && e.message},${wrap.time},${wrap.timeoutTime}`);
                }
            }
        }
        return wraps;
    }

    public save(wraps: AnnouncementModel[]) {
        try {
            this.cache_.save(wraps);
        } catch (e: any) {
            MyLogger.error(`--1--缓存公告数据失败:${e && e.message}`);
        }
    }
}
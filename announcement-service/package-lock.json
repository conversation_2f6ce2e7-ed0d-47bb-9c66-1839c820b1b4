{"name": "zhaosu-announcement-service", "version": "1.0.0", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "zhaosu-announcement-service", "version": "1.0.0", "license": "ISC", "dependencies": {"@ltaq/mysqlaa": "^3.1.0", "@ltaq/util": "^2.1.2", "@types/bent": "^7.3.3", "axios": "^0.24.0", "bent": "^7.3.12", "express": "^4.16.3", "moment": "^2.24.0", "multer": "^1.4.5-lts.1", "protobufjs": "^6.10.2", "ws": "^8.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/express": "^4.16.0", "@types/multer": "^1.4.7", "@types/mysql": "^2.15.5", "@types/ws": "^8.5.3", "@types/xlsx": "^0.0.36"}}, "node_modules/@ltaq/mysqlaa": {"version": "3.1.0", "resolved": "http://dev.ltaq.com:4873/@ltaq%2fmysqlaa/-/mysqlaa-3.1.0.tgz", "integrity": "sha512-+eDaxtudKvaRVl/GjtPdpLyWbMthVWXwbs8r6my8g8Ra44AYphSPxr53OJIq2VDce6DkjzUU5K0tCvyhCcQ/0w==", "license": "UNLICENSED", "dependencies": {"mysql": "^2.16.0"}}, "node_modules/@ltaq/util": {"version": "2.1.2", "resolved": "http://dev.ltaq.com:4873/@ltaq%2futil/-/util-2.1.2.tgz", "integrity": "sha512-MZU0AQxPs1MBriJvts7GKHQqnDCzZK1AIm5u6A0WUopyTGCcYJ+gmw0iKHjBSQdc1/rfbw8fNJwpt+p2kQhhVQ==", "license": "UNLICENSED", "dependencies": {"ws": "^7.2.1"}}, "node_modules/@ltaq/util/node_modules/ws": {"version": "7.5.7", "resolved": "http://dev.ltaq.com:4873/ws/-/ws-7.5.7.tgz", "integrity": "sha512-KMvVuFzpKBuiIXW3E4u3mySRO2/mCHSyZDJQM5NQ9Q9KHWHWh0NHgfbRMLLrceUK5qAL4ytALJbpRMjixFZh8A==", "license": "MIT", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/@protobufjs/aspromise": {"version": "1.1.2", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2faspromise/-/aspromise-1.1.2.tgz", "integrity": "sha1-m4sMxmPWaafY9vXQiToU00jzD78=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/base64": {"version": "1.1.2", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2fbase64/-/base64-1.1.2.tgz", "integrity": "sha1-TIVzDlm5ofHzSQR9vyQpYDS7JzU=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/codegen": {"version": "2.0.4", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2fcodegen/-/codegen-2.0.4.tgz", "integrity": "sha1-fvN/DQEPsCitGtWXIuUG2SYoFcs=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/eventemitter": {"version": "1.1.0", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2feventemitter/-/eventemitter-1.1.0.tgz", "integrity": "sha1-NVy8mLr61ZePntCV85diHx0Ga3A=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/fetch": {"version": "1.1.0", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2ffetch/-/fetch-1.1.0.tgz", "integrity": "sha1-upn7WYYUr2VwDBYZ/wbUVLDYTEU=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@protobufjs/aspromise": "^1.1.1", "@protobufjs/inquire": "^1.1.0"}}, "node_modules/@protobufjs/float": {"version": "1.0.2", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2ffloat/-/float-1.0.2.tgz", "integrity": "sha1-Xp4avctz/Ap8uLKR33jIy9l7h9E=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/inquire": {"version": "1.1.0", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2finquire/-/inquire-1.1.0.tgz", "integrity": "sha1-/yAOPnzyQp4tyvwRQIKOjMY48Ik=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/path": {"version": "1.1.2", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2fpath/-/path-1.1.2.tgz", "integrity": "sha1-bMKyDFya1q0NzP0hynZz2Nf79o0=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/pool": {"version": "1.1.0", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2fpool/-/pool-1.1.0.tgz", "integrity": "sha1-Cf0V8tbTq/qbZbw2ZQbWrXhG/1Q=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/utf8": {"version": "1.1.0", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2futf8/-/utf8-1.1.0.tgz", "integrity": "sha1-p3c2C1s5oaLlEG+OhY8v0tBgxXA=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@types/bent": {"version": "7.3.3", "resolved": "http://dev.ltaq.com:4873/@types%2fbent/-/bent-7.3.3.tgz", "integrity": "sha512-5NEIhVzHiZ6wMjFBmJ3gwjxwGug6amMoAn93rtDBttwrODxm+bt63u+MJA7H9NGGM4X1m73sJrAxDapktl036Q==", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/body-parser": {"version": "1.19.1", "resolved": "http://dev.ltaq.com:4873/@types%2fbody-parser/-/body-parser-1.19.1.tgz", "integrity": "sha1-DAF0xCp9AXuBgwPUtdlpywt1kpw=", "dev": true, "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/connect": {"version": "3.4.35", "resolved": "http://dev.ltaq.com:4873/@types%2fconnect/-/connect-3.4.35.tgz", "integrity": "sha1-X89q5EXkAh0fwiGaSHPMc6O7KtE=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/express": {"version": "4.17.13", "resolved": "http://dev.ltaq.com:4873/@types%2fexpress/-/express-4.17.13.tgz", "integrity": "sha1-p24plXKJmbq1GjP6vOHXBaNwkDQ=", "dev": true, "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "4.17.24", "resolved": "http://dev.ltaq.com:4873/@types%2fexpress-serve-static-core/-/express-serve-static-core-4.17.24.tgz", "integrity": "sha1-6kH5O/fg1ZzVp2ZlBo7WqraBXAc=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*"}}, "node_modules/@types/long": {"version": "4.0.1", "resolved": "http://dev.ltaq.com:4873/@types%2flong/-/long-4.0.1.tgz", "integrity": "sha1-RZxl+hhn2v5qjzIsTFFpVmPMVek=", "license": "MIT"}, "node_modules/@types/mime": {"version": "1.3.2", "resolved": "http://dev.ltaq.com:4873/@types%2fmime/-/mime-1.3.2.tgz", "integrity": "sha1-k+Jb+e51/g/YC1lLxP6w6GIRG1o=", "dev": true, "license": "MIT"}, "node_modules/@types/multer": {"version": "1.4.7", "resolved": "http://dev.ltaq.com:4873/@types%2fmulter/-/multer-1.4.7.tgz", "integrity": "sha1-ic8DVHwox7vMcm8Cnip2pyMsx54=", "dev": true, "license": "MIT", "dependencies": {"@types/express": "*"}}, "node_modules/@types/mysql": {"version": "2.15.19", "resolved": "http://dev.ltaq.com:4873/@types%2fmysql/-/mysql-2.15.19.tgz", "integrity": "sha1-0ViSe7fBp4935W3oYaOxXK4Oeu0=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/node": {"version": "16.6.1", "resolved": "http://dev.ltaq.com:4873/@types%2fnode/-/node-16.6.1.tgz", "integrity": "sha1-ruYse5ZvVfxmx7bfodWNsqYW2mE=", "license": "MIT"}, "node_modules/@types/qs": {"version": "6.9.7", "resolved": "http://dev.ltaq.com:4873/@types%2fqs/-/qs-6.9.7.tgz", "integrity": "sha1-Y7t9Bn2xB8weRXwwO8JdUR/r9ss=", "dev": true, "license": "MIT"}, "node_modules/@types/range-parser": {"version": "1.2.4", "resolved": "http://dev.ltaq.com:4873/@types%2frange-parser/-/range-parser-1.2.4.tgz", "integrity": "sha1-zWZ7z90CUhOq+3ylkVqTJZCs3Nw=", "dev": true, "license": "MIT"}, "node_modules/@types/serve-static": {"version": "1.13.10", "resolved": "http://dev.ltaq.com:4873/@types%2fserve-static/-/serve-static-1.13.10.tgz", "integrity": "sha1-9eDOh5fS18xevtpIpSyWxPpHqNk=", "dev": true, "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/ws": {"version": "8.5.3", "resolved": "http://dev.ltaq.com:4873/@types%2fws/-/ws-8.5.3.tgz", "integrity": "sha512-6YOoWjruKj1uLf3INHH7D3qTXwFfEsg1kf3c0uDdSBJwfa/llkwIjrAGV7j7mVgGNbzTQ3HiHKKDXl6bJPD97w==", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/xlsx": {"version": "0.0.36", "resolved": "http://dev.ltaq.com:4873/@types%2fxlsx/-/xlsx-0.0.36.tgz", "integrity": "sha1-tQYgA+XFN0q08I/dO/adpNQBOvg=", "deprecated": "This is a stub types definition for xlsx (https://github.com/sheetjs/js-xlsx). xlsx provides its own type definitions, so you don't need @types/xlsx installed!", "dev": true, "license": "MIT", "dependencies": {"xlsx": "*"}}, "node_modules/accepts": {"version": "1.3.7", "resolved": "http://dev.ltaq.com:4873/accepts/-/accepts-1.3.7.tgz", "integrity": "sha1-UxvHJlF6OytB+FACHGzBXqq1B80=", "license": "MIT", "dependencies": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/adler-32": {"version": "1.3.1", "resolved": "http://dev.ltaq.com:4873/adler-32/-/adler-32-1.3.1.tgz", "integrity": "sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A==", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/append-field": {"version": "1.0.0", "resolved": "http://dev.ltaq.com:4873/append-field/-/append-field-1.0.0.tgz", "integrity": "sha1-HjRA6RXwsSA9I3SOeO3XubW0PlY=", "license": "MIT"}, "node_modules/array-flatten": {"version": "1.1.1", "resolved": "http://dev.ltaq.com:4873/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=", "license": "MIT"}, "node_modules/axios": {"version": "0.24.0", "resolved": "http://dev.ltaq.com:4873/axios/-/axios-0.24.0.tgz", "integrity": "sha1-gE5voeS5xSiFAd2d/1anoJQNINY=", "license": "MIT", "dependencies": {"follow-redirects": "^1.14.4"}}, "node_modules/bent": {"version": "7.3.12", "resolved": "http://dev.ltaq.com:4873/bent/-/bent-7.3.12.tgz", "integrity": "sha1-4KJ3XUQl52dMZLeLJCr09J2msDU=", "license": "Apache-2.0", "dependencies": {"bytesish": "^0.4.1", "caseless": "~0.12.0", "is-stream": "^2.0.0"}}, "node_modules/bignumber.js": {"version": "9.0.0", "resolved": "http://dev.ltaq.com:4873/bignumber.js/-/bignumber.js-9.0.0.tgz", "integrity": "sha1-gFiA+Eoym16sbny2+CdLbYK98HU=", "license": "MIT", "engines": {"node": "*"}}, "node_modules/body-parser": {"version": "1.19.0", "resolved": "http://dev.ltaq.com:4873/body-parser/-/body-parser-1.19.0.tgz", "integrity": "sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io=", "license": "MIT", "dependencies": {"bytes": "3.1.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.2", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "on-finished": "~2.3.0", "qs": "6.7.0", "raw-body": "2.4.0", "type-is": "~1.6.17"}, "engines": {"node": ">= 0.8"}}, "node_modules/buffer-from": {"version": "1.1.2", "resolved": "http://dev.ltaq.com:4873/buffer-from/-/buffer-from-1.1.2.tgz", "integrity": "sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=", "license": "MIT"}, "node_modules/busboy": {"version": "1.6.0", "resolved": "http://dev.ltaq.com:4873/busboy/-/busboy-1.6.0.tgz", "integrity": "sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==", "dependencies": {"streamsearch": "^1.1.0"}, "engines": {"node": ">=10.16.0"}}, "node_modules/bytes": {"version": "3.1.0", "resolved": "http://dev.ltaq.com:4873/bytes/-/bytes-3.1.0.tgz", "integrity": "sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/bytesish": {"version": "0.4.4", "resolved": "http://dev.ltaq.com:4873/bytesish/-/bytesish-0.4.4.tgz", "integrity": "sha1-87U1oPEVN0dCeu4nJWdIz/kjR+Y=", "license": "(Apache-2.0 AND MIT)"}, "node_modules/caseless": {"version": "0.12.0", "resolved": "http://dev.ltaq.com:4873/caseless/-/caseless-0.12.0.tgz", "integrity": "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=", "license": "Apache-2.0"}, "node_modules/cfb": {"version": "1.2.2", "resolved": "http://dev.ltaq.com:4873/cfb/-/cfb-1.2.2.tgz", "integrity": "sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA==", "license": "Apache-2.0", "dependencies": {"adler-32": "~1.3.0", "crc-32": "~1.2.0"}, "engines": {"node": ">=0.8"}}, "node_modules/codepage": {"version": "1.15.0", "resolved": "http://dev.ltaq.com:4873/codepage/-/codepage-1.15.0.tgz", "integrity": "sha1-LgBRkCSzlCTsZu6z7AcifmkmGKs=", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/concat-stream": {"version": "1.6.2", "resolved": "http://dev.ltaq.com:4873/concat-stream/-/concat-stream-1.6.2.tgz", "integrity": "sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=", "engines": ["node >= 0.8"], "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "node_modules/content-disposition": {"version": "0.5.3", "resolved": "http://dev.ltaq.com:4873/content-disposition/-/content-disposition-0.5.3.tgz", "integrity": "sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=", "license": "MIT", "dependencies": {"safe-buffer": "5.1.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.4", "resolved": "http://dev.ltaq.com:4873/content-type/-/content-type-1.0.4.tgz", "integrity": "sha1-4TjMdeBAxyexlm/l5fjJruJW/js=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie": {"version": "0.4.0", "resolved": "http://dev.ltaq.com:4873/cookie/-/cookie-0.4.0.tgz", "integrity": "sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "resolved": "http://dev.ltaq.com:4873/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw=", "license": "MIT"}, "node_modules/core-util-is": {"version": "1.0.2", "resolved": "http://dev.ltaq.com:4873/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=", "license": "MIT"}, "node_modules/crc-32": {"version": "1.2.2", "resolved": "http://dev.ltaq.com:4873/crc-32/-/crc-32-1.2.2.tgz", "integrity": "sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==", "license": "Apache-2.0", "bin": {"crc32": "bin/crc32.njs"}, "engines": {"node": ">=0.8"}}, "node_modules/debug": {"version": "2.6.9", "resolved": "http://dev.ltaq.com:4873/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/depd": {"version": "1.1.2", "resolved": "http://dev.ltaq.com:4873/depd/-/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/destroy": {"version": "1.0.4", "resolved": "http://dev.ltaq.com:4873/destroy/-/destroy-1.0.4.tgz", "integrity": "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=", "license": "MIT"}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "http://dev.ltaq.com:4873/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=", "license": "MIT"}, "node_modules/encodeurl": {"version": "1.0.2", "resolved": "http://dev.ltaq.com:4873/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "http://dev.ltaq.com:4873/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "license": "MIT"}, "node_modules/etag": {"version": "1.8.1", "resolved": "http://dev.ltaq.com:4873/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/express": {"version": "4.17.1", "resolved": "http://dev.ltaq.com:4873/express/-/express-4.17.1.tgz", "integrity": "sha1-RJH8OGBc9R+GKdOcK10Cb5ikwTQ=", "license": "MIT", "dependencies": {"accepts": "~1.3.7", "array-flatten": "1.1.1", "body-parser": "1.19.0", "content-disposition": "0.5.3", "content-type": "~1.0.4", "cookie": "0.4.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "~1.1.2", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "~1.1.2", "fresh": "0.5.2", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.5", "qs": "6.7.0", "range-parser": "~1.2.1", "safe-buffer": "5.1.2", "send": "0.17.1", "serve-static": "1.14.1", "setprototypeof": "1.1.1", "statuses": "~1.5.0", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/finalhandler": {"version": "1.1.2", "resolved": "http://dev.ltaq.com:4873/finalhandler/-/finalhandler-1.1.2.tgz", "integrity": "sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/follow-redirects": {"version": "1.14.5", "resolved": "http://dev.ltaq.com:4873/follow-redirects/-/follow-redirects-1.14.5.tgz", "integrity": "sha1-8JpYSJgdPHcrU5Iwl3hSP42Fw4E=", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/forwarded": {"version": "0.2.0", "resolved": "http://dev.ltaq.com:4873/forwarded/-/forwarded-0.2.0.tgz", "integrity": "sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/frac": {"version": "1.1.2", "resolved": "http://dev.ltaq.com:4873/frac/-/frac-1.1.2.tgz", "integrity": "sha1-PXT39keMiKG1AgMG10fcYxPHTQs=", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/fresh": {"version": "0.5.2", "resolved": "http://dev.ltaq.com:4873/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/http-errors": {"version": "1.7.2", "resolved": "http://dev.ltaq.com:4873/http-errors/-/http-errors-1.7.2.tgz", "integrity": "sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8=", "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/http-errors/node_modules/inherits": {"version": "2.0.3", "resolved": "http://dev.ltaq.com:4873/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "license": "ISC"}, "node_modules/iconv-lite": {"version": "0.4.24", "resolved": "http://dev.ltaq.com:4873/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "http://dev.ltaq.com:4873/inherits/-/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "license": "ISC"}, "node_modules/ipaddr.js": {"version": "1.9.1", "resolved": "http://dev.ltaq.com:4873/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-stream": {"version": "2.0.1", "resolved": "http://dev.ltaq.com:4873/is-stream/-/is-stream-2.0.1.tgz", "integrity": "sha1-+sHj1TuXrVqdCunO8jifWBClwHc=", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/isarray": {"version": "1.0.0", "resolved": "http://dev.ltaq.com:4873/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "license": "MIT"}, "node_modules/long": {"version": "4.0.0", "resolved": "http://dev.ltaq.com:4873/long/-/long-4.0.0.tgz", "integrity": "sha1-mntxz7fTYaGU6lVSQckvdGjVvyg=", "license": "Apache-2.0"}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "http://dev.ltaq.com:4873/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/merge-descriptors": {"version": "1.0.1", "resolved": "http://dev.ltaq.com:4873/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=", "license": "MIT"}, "node_modules/methods": {"version": "1.1.2", "resolved": "http://dev.ltaq.com:4873/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime": {"version": "1.6.0", "resolved": "http://dev.ltaq.com:4873/mime/-/mime-1.6.0.tgz", "integrity": "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.49.0", "resolved": "http://dev.ltaq.com:4873/mime-db/-/mime-db-1.49.0.tgz", "integrity": "sha1-89/eYMmenPO8lwHWh3ePU3ABy+0=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.32", "resolved": "http://dev.ltaq.com:4873/mime-types/-/mime-types-2.1.32.tgz", "integrity": "sha1-HQDonn3n/gIAjbYQAdngKFJnD9U=", "license": "MIT", "dependencies": {"mime-db": "1.49.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "http://dev.ltaq.com:4873/minimist/-/minimist-1.2.8.tgz", "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/mkdirp": {"version": "0.5.6", "resolved": "http://dev.ltaq.com:4873/mkdirp/-/mkdirp-0.5.6.tgz", "integrity": "sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==", "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/moment": {"version": "2.29.1", "resolved": "http://dev.ltaq.com:4873/moment/-/moment-2.29.1.tgz", "integrity": "sha1-sr52n6MZQL6e7qZGnAdeNQBvo9M=", "license": "MIT", "engines": {"node": "*"}}, "node_modules/ms": {"version": "2.0.0", "resolved": "http://dev.ltaq.com:4873/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/multer": {"version": "1.4.5-lts.1", "resolved": "http://dev.ltaq.com:4873/multer/-/multer-1.4.5-lts.1.tgz", "integrity": "sha512-ywPWvcDMeH+z9gQq5qYHCCy+ethsk4goepZ45GLD63fOu0YcNecQxi64nDs3qluZB+murG3/D4dJ7+dGctcCQQ==", "license": "MIT", "dependencies": {"append-field": "^1.0.0", "busboy": "^1.0.0", "concat-stream": "^1.5.2", "mkdirp": "^0.5.4", "object-assign": "^4.1.1", "type-is": "^1.6.4", "xtend": "^4.0.0"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/mysql": {"version": "2.18.1", "resolved": "http://dev.ltaq.com:4873/mysql/-/mysql-2.18.1.tgz", "integrity": "sha1-IlQUOFXFqMc4JeRSK68uoCF2Zxc=", "license": "MIT", "dependencies": {"bignumber.js": "9.0.0", "readable-stream": "2.3.7", "safe-buffer": "5.1.2", "sqlstring": "2.3.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/negotiator": {"version": "0.6.2", "resolved": "http://dev.ltaq.com:4873/negotiator/-/negotiator-0.6.2.tgz", "integrity": "sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "http://dev.ltaq.com:4873/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/on-finished": {"version": "2.3.0", "resolved": "http://dev.ltaq.com:4873/on-finished/-/on-finished-2.3.0.tgz", "integrity": "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "http://dev.ltaq.com:4873/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/path-to-regexp": {"version": "0.1.7", "resolved": "http://dev.ltaq.com:4873/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "integrity": "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=", "license": "MIT"}, "node_modules/process-nextick-args": {"version": "2.0.1", "resolved": "http://dev.ltaq.com:4873/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha1-eCDZsWEgzFXKmud5JoCufbptf+I=", "license": "MIT"}, "node_modules/protobufjs": {"version": "6.11.2", "resolved": "http://dev.ltaq.com:4873/protobufjs/-/protobufjs-6.11.2.tgz", "integrity": "sha1-3jn6vU7TK+qgjpux4w0IVEwe34s=", "hasInstallScript": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/long": "^4.0.1", "@types/node": ">=13.7.0", "long": "^4.0.0"}, "bin": {"pbjs": "bin/pbjs", "pbts": "bin/pbts"}}, "node_modules/proxy-addr": {"version": "2.0.7", "resolved": "http://dev.ltaq.com:4873/proxy-addr/-/proxy-addr-2.0.7.tgz", "integrity": "sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=", "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/qs": {"version": "6.7.0", "resolved": "http://dev.ltaq.com:4873/qs/-/qs-6.7.0.tgz", "integrity": "sha1-QdwaAV49WB8WIXdr4xr7KHapsbw=", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "node_modules/range-parser": {"version": "1.2.1", "resolved": "http://dev.ltaq.com:4873/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.4.0", "resolved": "http://dev.ltaq.com:4873/raw-body/-/raw-body-2.4.0.tgz", "integrity": "sha1-oc5vucm8NWylLoklarWQWeE9AzI=", "license": "MIT", "dependencies": {"bytes": "3.1.0", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/readable-stream": {"version": "2.3.7", "resolved": "http://dev.ltaq.com:4873/readable-stream/-/readable-stream-2.3.7.tgz", "integrity": "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/safe-buffer": {"version": "5.1.2", "resolved": "http://dev.ltaq.com:4873/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "license": "MIT"}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "http://dev.ltaq.com:4873/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "license": "MIT"}, "node_modules/send": {"version": "0.17.1", "resolved": "http://dev.ltaq.com:4873/send/-/send-0.17.1.tgz", "integrity": "sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg=", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "~1.1.2", "destroy": "~1.0.4", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "~1.7.2", "mime": "1.6.0", "ms": "2.1.1", "on-finished": "~2.3.0", "range-parser": "~1.2.1", "statuses": "~1.5.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/ms": {"version": "2.1.1", "resolved": "http://dev.ltaq.com:4873/ms/-/ms-2.1.1.tgz", "integrity": "sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo=", "license": "MIT"}, "node_modules/serve-static": {"version": "1.14.1", "resolved": "http://dev.ltaq.com:4873/serve-static/-/serve-static-1.14.1.tgz", "integrity": "sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk=", "license": "MIT", "dependencies": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.17.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/setprototypeof": {"version": "1.1.1", "resolved": "http://dev.ltaq.com:4873/setprototypeof/-/setprototypeof-1.1.1.tgz", "integrity": "sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM=", "license": "ISC"}, "node_modules/sqlstring": {"version": "2.3.1", "resolved": "http://dev.ltaq.com:4873/sqlstring/-/sqlstring-2.3.1.tgz", "integrity": "sha1-R1OT/56RR5rqYtyvDKPRSYOn+0A=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/ssf": {"version": "0.11.2", "resolved": "http://dev.ltaq.com:4873/ssf/-/ssf-0.11.2.tgz", "integrity": "sha1-C5lpiyN1SNCI/EPN8rcMGnUSwGw=", "license": "Apache-2.0", "dependencies": {"frac": "~1.1.2"}, "engines": {"node": ">=0.8"}}, "node_modules/statuses": {"version": "1.5.0", "resolved": "http://dev.ltaq.com:4873/statuses/-/statuses-1.5.0.tgz", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/streamsearch": {"version": "1.1.0", "resolved": "http://dev.ltaq.com:4873/streamsearch/-/streamsearch-1.1.0.tgz", "integrity": "sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==", "engines": {"node": ">=10.0.0"}}, "node_modules/string_decoder": {"version": "1.1.1", "resolved": "http://dev.ltaq.com:4873/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/toidentifier": {"version": "1.0.0", "resolved": "http://dev.ltaq.com:4873/toidentifier/-/toidentifier-1.0.0.tgz", "integrity": "sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM=", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/type-is": {"version": "1.6.18", "resolved": "http://dev.ltaq.com:4873/type-is/-/type-is-1.6.18.tgz", "integrity": "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=", "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typedarray": {"version": "0.0.6", "resolved": "http://dev.ltaq.com:4873/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=", "license": "MIT"}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "http://dev.ltaq.com:4873/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "http://dev.ltaq.com:4873/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "license": "MIT"}, "node_modules/utils-merge": {"version": "1.0.1", "resolved": "http://dev.ltaq.com:4873/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/vary": {"version": "1.1.2", "resolved": "http://dev.ltaq.com:4873/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/wmf": {"version": "1.0.2", "resolved": "http://dev.ltaq.com:4873/wmf/-/wmf-1.0.2.tgz", "integrity": "sha1-fRnWIQcaCMK9xrfmiKnENSmMwto=", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/word": {"version": "0.3.0", "resolved": "http://dev.ltaq.com:4873/word/-/word-0.3.0.tgz", "integrity": "sha1-hUIVfk+OhJ9KNjooiZLUdhLbmWE=", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/ws": {"version": "8.5.0", "resolved": "http://dev.ltaq.com:4873/ws/-/ws-8.5.0.tgz", "integrity": "sha512-BWX0SWVgLPzYwF8lTzEy1egjhS4S4OEAHfsO8o65WOVsrnSRGaSiUaa9e0ggGlkMTtBlmOpEXiie9RUcBO86qg==", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xlsx": {"version": "0.18.5", "resolved": "http://dev.ltaq.com:4873/xlsx/-/xlsx-0.18.5.tgz", "integrity": "sha512-dmg3LCjBPHZnQp5/F/+nnTa+miPJxUXB6vtk42YjBBKayDNagxGEeIdWApkYPOf3Z3pm3k62Knjzp7lMeTEtFQ==", "license": "Apache-2.0", "dependencies": {"adler-32": "~1.3.0", "cfb": "~1.2.1", "codepage": "~1.15.0", "crc-32": "~1.2.1", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "bin": {"xlsx": "bin/xlsx.njs"}, "engines": {"node": ">=0.8"}}, "node_modules/xtend": {"version": "4.0.2", "resolved": "http://dev.ltaq.com:4873/xtend/-/xtend-4.0.2.tgz", "integrity": "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=", "license": "MIT", "engines": {"node": ">=0.4"}}}, "dependencies": {"@ltaq/mysqlaa": {"version": "3.1.0", "resolved": "http://dev.ltaq.com:4873/@ltaq%2fmysqlaa/-/mysqlaa-3.1.0.tgz", "integrity": "sha512-+eDaxtudKvaRVl/GjtPdpLyWbMthVWXwbs8r6my8g8Ra44AYphSPxr53OJIq2VDce6DkjzUU5K0tCvyhCcQ/0w==", "requires": {"mysql": "^2.16.0"}}, "@ltaq/util": {"version": "2.1.2", "resolved": "http://dev.ltaq.com:4873/@ltaq%2futil/-/util-2.1.2.tgz", "integrity": "sha512-MZU0AQxPs1MBriJvts7GKHQqnDCzZK1AIm5u6A0WUopyTGCcYJ+gmw0iKHjBSQdc1/rfbw8fNJwpt+p2kQhhVQ==", "requires": {"ws": "^7.2.1"}, "dependencies": {"ws": {"version": "7.5.7", "resolved": "http://dev.ltaq.com:4873/ws/-/ws-7.5.7.tgz", "integrity": "sha512-KMvVuFzpKBuiIXW3E4u3mySRO2/mCHSyZDJQM5NQ9Q9KHWHWh0NHgfbRMLLrceUK5qAL4ytALJbpRMjixFZh8A==", "requires": {}}}}, "@protobufjs/aspromise": {"version": "1.1.2", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2faspromise/-/aspromise-1.1.2.tgz", "integrity": "sha1-m4sMxmPWaafY9vXQiToU00jzD78="}, "@protobufjs/base64": {"version": "1.1.2", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2fbase64/-/base64-1.1.2.tgz", "integrity": "sha1-TIVzDlm5ofHzSQR9vyQpYDS7JzU="}, "@protobufjs/codegen": {"version": "2.0.4", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2fcodegen/-/codegen-2.0.4.tgz", "integrity": "sha1-fvN/DQEPsCitGtWXIuUG2SYoFcs="}, "@protobufjs/eventemitter": {"version": "1.1.0", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2feventemitter/-/eventemitter-1.1.0.tgz", "integrity": "sha1-NVy8mLr61ZePntCV85diHx0Ga3A="}, "@protobufjs/fetch": {"version": "1.1.0", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2ffetch/-/fetch-1.1.0.tgz", "integrity": "sha1-upn7WYYUr2VwDBYZ/wbUVLDYTEU=", "requires": {"@protobufjs/aspromise": "^1.1.1", "@protobufjs/inquire": "^1.1.0"}}, "@protobufjs/float": {"version": "1.0.2", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2ffloat/-/float-1.0.2.tgz", "integrity": "sha1-Xp4avctz/Ap8uLKR33jIy9l7h9E="}, "@protobufjs/inquire": {"version": "1.1.0", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2finquire/-/inquire-1.1.0.tgz", "integrity": "sha1-/yAOPnzyQp4tyvwRQIKOjMY48Ik="}, "@protobufjs/path": {"version": "1.1.2", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2fpath/-/path-1.1.2.tgz", "integrity": "sha1-bMKyDFya1q0NzP0hynZz2Nf79o0="}, "@protobufjs/pool": {"version": "1.1.0", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2fpool/-/pool-1.1.0.tgz", "integrity": "sha1-Cf0V8tbTq/qbZbw2ZQbWrXhG/1Q="}, "@protobufjs/utf8": {"version": "1.1.0", "resolved": "http://dev.ltaq.com:4873/@protobufjs%2futf8/-/utf8-1.1.0.tgz", "integrity": "sha1-p3c2C1s5oaLlEG+OhY8v0tBgxXA="}, "@types/bent": {"version": "7.3.3", "resolved": "http://dev.ltaq.com:4873/@types%2fbent/-/bent-7.3.3.tgz", "integrity": "sha512-5NEIhVzHiZ6wMjFBmJ3gwjxwGug6amMoAn93rtDBttwrODxm+bt63u+MJA7H9NGGM4X1m73sJrAxDapktl036Q==", "requires": {"@types/node": "*"}}, "@types/body-parser": {"version": "1.19.1", "resolved": "http://dev.ltaq.com:4873/@types%2fbody-parser/-/body-parser-1.19.1.tgz", "integrity": "sha1-DAF0xCp9AXuBgwPUtdlpywt1kpw=", "dev": true, "requires": {"@types/connect": "*", "@types/node": "*"}}, "@types/connect": {"version": "3.4.35", "resolved": "http://dev.ltaq.com:4873/@types%2fconnect/-/connect-3.4.35.tgz", "integrity": "sha1-X89q5EXkAh0fwiGaSHPMc6O7KtE=", "dev": true, "requires": {"@types/node": "*"}}, "@types/express": {"version": "4.17.13", "resolved": "http://dev.ltaq.com:4873/@types%2fexpress/-/express-4.17.13.tgz", "integrity": "sha1-p24plXKJmbq1GjP6vOHXBaNwkDQ=", "dev": true, "requires": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}}, "@types/express-serve-static-core": {"version": "4.17.24", "resolved": "http://dev.ltaq.com:4873/@types%2fexpress-serve-static-core/-/express-serve-static-core-4.17.24.tgz", "integrity": "sha1-6kH5O/fg1ZzVp2ZlBo7WqraBXAc=", "dev": true, "requires": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*"}}, "@types/long": {"version": "4.0.1", "resolved": "http://dev.ltaq.com:4873/@types%2flong/-/long-4.0.1.tgz", "integrity": "sha1-RZxl+hhn2v5qjzIsTFFpVmPMVek="}, "@types/mime": {"version": "1.3.2", "resolved": "http://dev.ltaq.com:4873/@types%2fmime/-/mime-1.3.2.tgz", "integrity": "sha1-k+Jb+e51/g/YC1lLxP6w6GIRG1o=", "dev": true}, "@types/multer": {"version": "1.4.7", "resolved": "http://dev.ltaq.com:4873/@types%2fmulter/-/multer-1.4.7.tgz", "integrity": "sha1-ic8DVHwox7vMcm8Cnip2pyMsx54=", "dev": true, "requires": {"@types/express": "*"}}, "@types/mysql": {"version": "2.15.19", "resolved": "http://dev.ltaq.com:4873/@types%2fmysql/-/mysql-2.15.19.tgz", "integrity": "sha1-0ViSe7fBp4935W3oYaOxXK4Oeu0=", "dev": true, "requires": {"@types/node": "*"}}, "@types/node": {"version": "16.6.1", "resolved": "http://dev.ltaq.com:4873/@types%2fnode/-/node-16.6.1.tgz", "integrity": "sha1-ruYse5ZvVfxmx7bfodWNsqYW2mE="}, "@types/qs": {"version": "6.9.7", "resolved": "http://dev.ltaq.com:4873/@types%2fqs/-/qs-6.9.7.tgz", "integrity": "sha1-Y7t9Bn2xB8weRXwwO8JdUR/r9ss=", "dev": true}, "@types/range-parser": {"version": "1.2.4", "resolved": "http://dev.ltaq.com:4873/@types%2frange-parser/-/range-parser-1.2.4.tgz", "integrity": "sha1-zWZ7z90CUhOq+3ylkVqTJZCs3Nw=", "dev": true}, "@types/serve-static": {"version": "1.13.10", "resolved": "http://dev.ltaq.com:4873/@types%2fserve-static/-/serve-static-1.13.10.tgz", "integrity": "sha1-9eDOh5fS18xevtpIpSyWxPpHqNk=", "dev": true, "requires": {"@types/mime": "^1", "@types/node": "*"}}, "@types/ws": {"version": "8.5.3", "resolved": "http://dev.ltaq.com:4873/@types%2fws/-/ws-8.5.3.tgz", "integrity": "sha512-6YOoWjruKj1uLf3INHH7D3qTXwFfEsg1kf3c0uDdSBJwfa/llkwIjrAGV7j7mVgGNbzTQ3HiHKKDXl6bJPD97w==", "dev": true, "requires": {"@types/node": "*"}}, "@types/xlsx": {"version": "0.0.36", "resolved": "http://dev.ltaq.com:4873/@types%2fxlsx/-/xlsx-0.0.36.tgz", "integrity": "sha1-tQYgA+XFN0q08I/dO/adpNQBOvg=", "dev": true, "requires": {"xlsx": "*"}}, "accepts": {"version": "1.3.7", "resolved": "http://dev.ltaq.com:4873/accepts/-/accepts-1.3.7.tgz", "integrity": "sha1-UxvHJlF6OytB+FACHGzBXqq1B80=", "requires": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}}, "adler-32": {"version": "1.3.1", "resolved": "http://dev.ltaq.com:4873/adler-32/-/adler-32-1.3.1.tgz", "integrity": "sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A=="}, "append-field": {"version": "1.0.0", "resolved": "http://dev.ltaq.com:4873/append-field/-/append-field-1.0.0.tgz", "integrity": "sha1-HjRA6RXwsSA9I3SOeO3XubW0PlY="}, "array-flatten": {"version": "1.1.1", "resolved": "http://dev.ltaq.com:4873/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="}, "axios": {"version": "0.24.0", "resolved": "http://dev.ltaq.com:4873/axios/-/axios-0.24.0.tgz", "integrity": "sha1-gE5voeS5xSiFAd2d/1anoJQNINY=", "requires": {"follow-redirects": "^1.14.4"}}, "bent": {"version": "7.3.12", "resolved": "http://dev.ltaq.com:4873/bent/-/bent-7.3.12.tgz", "integrity": "sha1-4KJ3XUQl52dMZLeLJCr09J2msDU=", "requires": {"bytesish": "^0.4.1", "caseless": "~0.12.0", "is-stream": "^2.0.0"}}, "bignumber.js": {"version": "9.0.0", "resolved": "http://dev.ltaq.com:4873/bignumber.js/-/bignumber.js-9.0.0.tgz", "integrity": "sha1-gFiA+Eoym16sbny2+CdLbYK98HU="}, "body-parser": {"version": "1.19.0", "resolved": "http://dev.ltaq.com:4873/body-parser/-/body-parser-1.19.0.tgz", "integrity": "sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io=", "requires": {"bytes": "3.1.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.2", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "on-finished": "~2.3.0", "qs": "6.7.0", "raw-body": "2.4.0", "type-is": "~1.6.17"}}, "buffer-from": {"version": "1.1.2", "resolved": "http://dev.ltaq.com:4873/buffer-from/-/buffer-from-1.1.2.tgz", "integrity": "sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U="}, "busboy": {"version": "1.6.0", "resolved": "http://dev.ltaq.com:4873/busboy/-/busboy-1.6.0.tgz", "integrity": "sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==", "requires": {"streamsearch": "^1.1.0"}}, "bytes": {"version": "3.1.0", "resolved": "http://dev.ltaq.com:4873/bytes/-/bytes-3.1.0.tgz", "integrity": "sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY="}, "bytesish": {"version": "0.4.4", "resolved": "http://dev.ltaq.com:4873/bytesish/-/bytesish-0.4.4.tgz", "integrity": "sha1-87U1oPEVN0dCeu4nJWdIz/kjR+Y="}, "caseless": {"version": "0.12.0", "resolved": "http://dev.ltaq.com:4873/caseless/-/caseless-0.12.0.tgz", "integrity": "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw="}, "cfb": {"version": "1.2.2", "resolved": "http://dev.ltaq.com:4873/cfb/-/cfb-1.2.2.tgz", "integrity": "sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA==", "requires": {"adler-32": "~1.3.0", "crc-32": "~1.2.0"}}, "codepage": {"version": "1.15.0", "resolved": "http://dev.ltaq.com:4873/codepage/-/codepage-1.15.0.tgz", "integrity": "sha1-LgBRkCSzlCTsZu6z7AcifmkmGKs="}, "concat-stream": {"version": "1.6.2", "resolved": "http://dev.ltaq.com:4873/concat-stream/-/concat-stream-1.6.2.tgz", "integrity": "sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=", "requires": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "content-disposition": {"version": "0.5.3", "resolved": "http://dev.ltaq.com:4873/content-disposition/-/content-disposition-0.5.3.tgz", "integrity": "sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=", "requires": {"safe-buffer": "5.1.2"}}, "content-type": {"version": "1.0.4", "resolved": "http://dev.ltaq.com:4873/content-type/-/content-type-1.0.4.tgz", "integrity": "sha1-4TjMdeBAxyexlm/l5fjJruJW/js="}, "cookie": {"version": "0.4.0", "resolved": "http://dev.ltaq.com:4873/cookie/-/cookie-0.4.0.tgz", "integrity": "sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo="}, "cookie-signature": {"version": "1.0.6", "resolved": "http://dev.ltaq.com:4873/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="}, "core-util-is": {"version": "1.0.2", "resolved": "http://dev.ltaq.com:4873/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="}, "crc-32": {"version": "1.2.2", "resolved": "http://dev.ltaq.com:4873/crc-32/-/crc-32-1.2.2.tgz", "integrity": "sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ=="}, "debug": {"version": "2.6.9", "resolved": "http://dev.ltaq.com:4873/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "depd": {"version": "1.1.2", "resolved": "http://dev.ltaq.com:4873/depd/-/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="}, "destroy": {"version": "1.0.4", "resolved": "http://dev.ltaq.com:4873/destroy/-/destroy-1.0.4.tgz", "integrity": "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="}, "ee-first": {"version": "1.1.1", "resolved": "http://dev.ltaq.com:4873/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="}, "encodeurl": {"version": "1.0.2", "resolved": "http://dev.ltaq.com:4873/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="}, "escape-html": {"version": "1.0.3", "resolved": "http://dev.ltaq.com:4873/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="}, "etag": {"version": "1.8.1", "resolved": "http://dev.ltaq.com:4873/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="}, "express": {"version": "4.17.1", "resolved": "http://dev.ltaq.com:4873/express/-/express-4.17.1.tgz", "integrity": "sha1-RJH8OGBc9R+GKdOcK10Cb5ikwTQ=", "requires": {"accepts": "~1.3.7", "array-flatten": "1.1.1", "body-parser": "1.19.0", "content-disposition": "0.5.3", "content-type": "~1.0.4", "cookie": "0.4.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "~1.1.2", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "~1.1.2", "fresh": "0.5.2", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.5", "qs": "6.7.0", "range-parser": "~1.2.1", "safe-buffer": "5.1.2", "send": "0.17.1", "serve-static": "1.14.1", "setprototypeof": "1.1.1", "statuses": "~1.5.0", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}}, "finalhandler": {"version": "1.1.2", "resolved": "http://dev.ltaq.com:4873/finalhandler/-/finalhandler-1.1.2.tgz", "integrity": "sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=", "requires": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}}, "follow-redirects": {"version": "1.14.5", "resolved": "http://dev.ltaq.com:4873/follow-redirects/-/follow-redirects-1.14.5.tgz", "integrity": "sha1-8JpYSJgdPHcrU5Iwl3hSP42Fw4E="}, "forwarded": {"version": "0.2.0", "resolved": "http://dev.ltaq.com:4873/forwarded/-/forwarded-0.2.0.tgz", "integrity": "sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE="}, "frac": {"version": "1.1.2", "resolved": "http://dev.ltaq.com:4873/frac/-/frac-1.1.2.tgz", "integrity": "sha1-PXT39keMiKG1AgMG10fcYxPHTQs="}, "fresh": {"version": "0.5.2", "resolved": "http://dev.ltaq.com:4873/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="}, "http-errors": {"version": "1.7.2", "resolved": "http://dev.ltaq.com:4873/http-errors/-/http-errors-1.7.2.tgz", "integrity": "sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8=", "requires": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "dependencies": {"inherits": {"version": "2.0.3", "resolved": "http://dev.ltaq.com:4873/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="}}}, "iconv-lite": {"version": "0.4.24", "resolved": "http://dev.ltaq.com:4873/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "inherits": {"version": "2.0.4", "resolved": "http://dev.ltaq.com:4873/inherits/-/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="}, "ipaddr.js": {"version": "1.9.1", "resolved": "http://dev.ltaq.com:4873/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM="}, "is-stream": {"version": "2.0.1", "resolved": "http://dev.ltaq.com:4873/is-stream/-/is-stream-2.0.1.tgz", "integrity": "sha1-+sHj1TuXrVqdCunO8jifWBClwHc="}, "isarray": {"version": "1.0.0", "resolved": "http://dev.ltaq.com:4873/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="}, "long": {"version": "4.0.0", "resolved": "http://dev.ltaq.com:4873/long/-/long-4.0.0.tgz", "integrity": "sha1-mntxz7fTYaGU6lVSQckvdGjVvyg="}, "media-typer": {"version": "0.3.0", "resolved": "http://dev.ltaq.com:4873/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="}, "merge-descriptors": {"version": "1.0.1", "resolved": "http://dev.ltaq.com:4873/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="}, "methods": {"version": "1.1.2", "resolved": "http://dev.ltaq.com:4873/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="}, "mime": {"version": "1.6.0", "resolved": "http://dev.ltaq.com:4873/mime/-/mime-1.6.0.tgz", "integrity": "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE="}, "mime-db": {"version": "1.49.0", "resolved": "http://dev.ltaq.com:4873/mime-db/-/mime-db-1.49.0.tgz", "integrity": "sha1-89/eYMmenPO8lwHWh3ePU3ABy+0="}, "mime-types": {"version": "2.1.32", "resolved": "http://dev.ltaq.com:4873/mime-types/-/mime-types-2.1.32.tgz", "integrity": "sha1-HQDonn3n/gIAjbYQAdngKFJnD9U=", "requires": {"mime-db": "1.49.0"}}, "minimist": {"version": "1.2.8", "resolved": "http://dev.ltaq.com:4873/minimist/-/minimist-1.2.8.tgz", "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA=="}, "mkdirp": {"version": "0.5.6", "resolved": "http://dev.ltaq.com:4873/mkdirp/-/mkdirp-0.5.6.tgz", "integrity": "sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==", "requires": {"minimist": "^1.2.6"}}, "moment": {"version": "2.29.1", "resolved": "http://dev.ltaq.com:4873/moment/-/moment-2.29.1.tgz", "integrity": "sha1-sr52n6MZQL6e7qZGnAdeNQBvo9M="}, "ms": {"version": "2.0.0", "resolved": "http://dev.ltaq.com:4873/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "multer": {"version": "1.4.5-lts.1", "resolved": "http://dev.ltaq.com:4873/multer/-/multer-1.4.5-lts.1.tgz", "integrity": "sha512-ywPWvcDMeH+z9gQq5qYHCCy+ethsk4goepZ45GLD63fOu0YcNecQxi64nDs3qluZB+murG3/D4dJ7+dGctcCQQ==", "requires": {"append-field": "^1.0.0", "busboy": "^1.0.0", "concat-stream": "^1.5.2", "mkdirp": "^0.5.4", "object-assign": "^4.1.1", "type-is": "^1.6.4", "xtend": "^4.0.0"}}, "mysql": {"version": "2.18.1", "resolved": "http://dev.ltaq.com:4873/mysql/-/mysql-2.18.1.tgz", "integrity": "sha1-IlQUOFXFqMc4JeRSK68uoCF2Zxc=", "requires": {"bignumber.js": "9.0.0", "readable-stream": "2.3.7", "safe-buffer": "5.1.2", "sqlstring": "2.3.1"}}, "negotiator": {"version": "0.6.2", "resolved": "http://dev.ltaq.com:4873/negotiator/-/negotiator-0.6.2.tgz", "integrity": "sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs="}, "object-assign": {"version": "4.1.1", "resolved": "http://dev.ltaq.com:4873/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="}, "on-finished": {"version": "2.3.0", "resolved": "http://dev.ltaq.com:4873/on-finished/-/on-finished-2.3.0.tgz", "integrity": "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=", "requires": {"ee-first": "1.1.1"}}, "parseurl": {"version": "1.3.3", "resolved": "http://dev.ltaq.com:4873/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="}, "path-to-regexp": {"version": "0.1.7", "resolved": "http://dev.ltaq.com:4873/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "integrity": "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="}, "process-nextick-args": {"version": "2.0.1", "resolved": "http://dev.ltaq.com:4873/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha1-eCDZsWEgzFXKmud5JoCufbptf+I="}, "protobufjs": {"version": "6.11.2", "resolved": "http://dev.ltaq.com:4873/protobufjs/-/protobufjs-6.11.2.tgz", "integrity": "sha1-3jn6vU7TK+qgjpux4w0IVEwe34s=", "requires": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/long": "^4.0.1", "@types/node": ">=13.7.0", "long": "^4.0.0"}}, "proxy-addr": {"version": "2.0.7", "resolved": "http://dev.ltaq.com:4873/proxy-addr/-/proxy-addr-2.0.7.tgz", "integrity": "sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=", "requires": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}}, "qs": {"version": "6.7.0", "resolved": "http://dev.ltaq.com:4873/qs/-/qs-6.7.0.tgz", "integrity": "sha1-QdwaAV49WB8WIXdr4xr7KHapsbw="}, "range-parser": {"version": "1.2.1", "resolved": "http://dev.ltaq.com:4873/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE="}, "raw-body": {"version": "2.4.0", "resolved": "http://dev.ltaq.com:4873/raw-body/-/raw-body-2.4.0.tgz", "integrity": "sha1-oc5vucm8NWylLoklarWQWeE9AzI=", "requires": {"bytes": "3.1.0", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}}, "readable-stream": {"version": "2.3.7", "resolved": "http://dev.ltaq.com:4873/readable-stream/-/readable-stream-2.3.7.tgz", "integrity": "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "safe-buffer": {"version": "5.1.2", "resolved": "http://dev.ltaq.com:4873/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="}, "safer-buffer": {"version": "2.1.2", "resolved": "http://dev.ltaq.com:4873/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="}, "send": {"version": "0.17.1", "resolved": "http://dev.ltaq.com:4873/send/-/send-0.17.1.tgz", "integrity": "sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg=", "requires": {"debug": "2.6.9", "depd": "~1.1.2", "destroy": "~1.0.4", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "~1.7.2", "mime": "1.6.0", "ms": "2.1.1", "on-finished": "~2.3.0", "range-parser": "~1.2.1", "statuses": "~1.5.0"}, "dependencies": {"ms": {"version": "2.1.1", "resolved": "http://dev.ltaq.com:4873/ms/-/ms-2.1.1.tgz", "integrity": "sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo="}}}, "serve-static": {"version": "1.14.1", "resolved": "http://dev.ltaq.com:4873/serve-static/-/serve-static-1.14.1.tgz", "integrity": "sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk=", "requires": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.17.1"}}, "setprototypeof": {"version": "1.1.1", "resolved": "http://dev.ltaq.com:4873/setprototypeof/-/setprototypeof-1.1.1.tgz", "integrity": "sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM="}, "sqlstring": {"version": "2.3.1", "resolved": "http://dev.ltaq.com:4873/sqlstring/-/sqlstring-2.3.1.tgz", "integrity": "sha1-R1OT/56RR5rqYtyvDKPRSYOn+0A="}, "ssf": {"version": "0.11.2", "resolved": "http://dev.ltaq.com:4873/ssf/-/ssf-0.11.2.tgz", "integrity": "sha1-C5lpiyN1SNCI/EPN8rcMGnUSwGw=", "requires": {"frac": "~1.1.2"}}, "statuses": {"version": "1.5.0", "resolved": "http://dev.ltaq.com:4873/statuses/-/statuses-1.5.0.tgz", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="}, "streamsearch": {"version": "1.1.0", "resolved": "http://dev.ltaq.com:4873/streamsearch/-/streamsearch-1.1.0.tgz", "integrity": "sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg=="}, "string_decoder": {"version": "1.1.1", "resolved": "http://dev.ltaq.com:4873/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "requires": {"safe-buffer": "~5.1.0"}}, "toidentifier": {"version": "1.0.0", "resolved": "http://dev.ltaq.com:4873/toidentifier/-/toidentifier-1.0.0.tgz", "integrity": "sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM="}, "type-is": {"version": "1.6.18", "resolved": "http://dev.ltaq.com:4873/type-is/-/type-is-1.6.18.tgz", "integrity": "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=", "requires": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}}, "typedarray": {"version": "0.0.6", "resolved": "http://dev.ltaq.com:4873/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c="}, "unpipe": {"version": "1.0.0", "resolved": "http://dev.ltaq.com:4873/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="}, "util-deprecate": {"version": "1.0.2", "resolved": "http://dev.ltaq.com:4873/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="}, "utils-merge": {"version": "1.0.1", "resolved": "http://dev.ltaq.com:4873/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="}, "vary": {"version": "1.1.2", "resolved": "http://dev.ltaq.com:4873/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="}, "wmf": {"version": "1.0.2", "resolved": "http://dev.ltaq.com:4873/wmf/-/wmf-1.0.2.tgz", "integrity": "sha1-fRnWIQcaCMK9xrfmiKnENSmMwto="}, "word": {"version": "0.3.0", "resolved": "http://dev.ltaq.com:4873/word/-/word-0.3.0.tgz", "integrity": "sha1-hUIVfk+OhJ9KNjooiZLUdhLbmWE="}, "ws": {"version": "8.5.0", "resolved": "http://dev.ltaq.com:4873/ws/-/ws-8.5.0.tgz", "integrity": "sha512-BWX0SWVgLPzYwF8lTzEy1egjhS4S4OEAHfsO8o65WOVsrnSRGaSiUaa9e0ggGlkMTtBlmOpEXiie9RUcBO86qg==", "requires": {}}, "xlsx": {"version": "0.18.5", "resolved": "http://dev.ltaq.com:4873/xlsx/-/xlsx-0.18.5.tgz", "integrity": "sha512-dmg3LCjBPHZnQp5/F/+nnTa+miPJxUXB6vtk42YjBBKayDNagxGEeIdWApkYPOf3Z3pm3k62Knjzp7lMeTEtFQ==", "requires": {"adler-32": "~1.3.0", "cfb": "~1.2.1", "codepage": "~1.15.0", "crc-32": "~1.2.1", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}}, "xtend": {"version": "4.0.2", "resolved": "http://dev.ltaq.com:4873/xtend/-/xtend-4.0.2.tgz", "integrity": "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q="}}}
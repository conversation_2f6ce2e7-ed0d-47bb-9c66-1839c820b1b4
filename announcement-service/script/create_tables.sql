
CREATE TABLE IF NOT EXISTS `zs_announcement` (
  `id` varchar(50) NOT NULL,
  `createUserName` varchar(50) DEFAULT NULL,
  `createUserId` int(11) DEFAULT NULL,
  `createOrgName` varchar(50) DEFAULT NULL,
  `createOrgId` int(11) DEFAULT NULL,
  `title` varchar(205) DEFAULT NULL,
  `content` text DEFAULT NULL,
  `receiveUsers` text DEFAULT NULL,
   `accessUsers` text DEFAULT NULL,
   `time` datetime DEFAULT NULL,
   `timeoutTime` datetime DEFAULT NULL,

   `deleted` int(10) unsigned DEFAULT NULL,
   `deleteTime` datetime DEFAULT NULL,
   KEY `Index 1` (`id`)
) ;

import {HttpService} from "./HttpService";
import {DbConnect} from "./db/DbConnect";
import {WebSocketPublisher} from "./websocket/WebSocketPublisher";
import AnnouncementHandler from "./handler/AnnouncementHandler";
import {AnnouncementRouter} from "./route/AnnouncementRouter";



const config = require('./config.json'); // 配置文件
let db = new DbConnect(config.database); // 数据库
let httpService = new HttpService(config.port); // HTTP
let publisher = new WebSocketPublisher(config.wsport);

let announcemengHandler = new AnnouncementHandler(db, publisher);


let announcement = new AnnouncementRouter(db, announcemengHandler);


publisher.bindHandler(announcemengHandler);
httpService.bindRouter(announcement.router);


httpService.start();
setTimeout(() => publisher.startUp(), 2000);



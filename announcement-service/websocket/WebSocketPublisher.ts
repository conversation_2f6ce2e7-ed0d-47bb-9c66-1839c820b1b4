import WebSocket, {Server} from "ws";
import * as http from 'http';
import {UserUtil} from "../Util/UserUtil";
import AnnouncementHandler from "../handler/AnnouncementHandler";
import {AnnouncementModel, FileModel, TrainingModel} from "../model/AnnouncementModel";

export class WebSocketPublisher {
    private wss?: Server;
    private effective_: Map<any, { createUserId: number, receiveUsers: Set<number>, pack: string }>;//msgId,{userId,receiveUsers,pack}

    private clientIds_ = new Map<WebSocket, Set<number>>();
    private announcementHandler_?: AnnouncementHandler;
    private portOrServer: number | http.Server;

    constructor(portOrServer: number | http.Server) {
        this.effective_ = new Map<any, { createUserId: number, receiveUsers: Set<number>, pack: string }>();
        this.portOrServer = portOrServer;
    }

    public startUp() {
        let portOrServer = this.portOrServer;
        if (typeof (portOrServer) === 'number') {
            this.wss = new Server({port: portOrServer});
        } else {
            this.wss = new Server({server: portOrServer});
        }

        this.wss.on("connection", (client) => {
            console.log('announcement:client connected.');
            client.on('error', (error) => console.log(error));
            client.on('close', () => console.log('alarm:client disconnected'));
            // for (let [, v] of this.effective_)//不鉴定权限全部都发
            //     client.send(v[1]);
            client.on('message', async (msg: string) => {
                const data = JSON.parse(msg);
                if (data.type === 'hello') {
                    const clientUserId = parseToken(data.data.token);
                    let clientAccessUsers = await this.getUserAccess(clientUserId);
                    let clientAccessUserSet = new Set(clientAccessUsers);
                    this.clientIds_.set(client, clientAccessUserSet);
                    for (let [, v] of this.effective_) {
                        //客户端拥有消息创建人的权限，且，客户端id没有已读
                        if (clientAccessUserSet.has(v.createUserId) && !v.receiveUsers.has(clientUserId)) {
                            client.send(v.pack);
                        }
                    }
                }
            });
        });
        setInterval(this.ping.bind(this), 3000);
    }

    public bindHandler(announcement: AnnouncementHandler) {
        this.announcementHandler_ = announcement;
        setTimeout(this.loadHistory.bind(this), 1000);
    }

    public loadHistory() {
        if (this.announcementHandler_) {
            let announcements: AnnouncementModel[] = this.announcementHandler_.records || [];
            if (announcements.length) {
                for (let ann of announcements) {
                    let msg = Object.assign({}, ann);
                    msg.receiveUsers = [];
                    let pack = JSON.stringify({action: "add", type: 'announcement', msg: msg});
                    this.effective_.set(ann.id, {
                        createUserId: ann.createUserId,
                        receiveUsers: new Set(ann.receiveUsers),
                        pack: pack
                    })
                }
            }
        }
    }


    private async getUserAccess(userId: number) {
        let data = await UserUtil.getUserAccess('webgis_d_announcement', userId);
        return data && data.access_user_ids || [];
    }

    private ping() {
        this.clientIds_.forEach((value, client) => {
            try {
                client.ping();
            } catch (e) {
                console.log(e);
            }
        })

    }

    public add(type: string, msg: any) {//msg:{userId:number}
        let pack = JSON.stringify({action: "add", type: type, msg: msg});
        this.clientIds_.forEach((clientAccessUserSet, c) => {
            if (clientAccessUserSet && clientAccessUserSet.has(msg.createUserId)) {
                c.send(pack);
            }
        })
        this.effective_.set(msg.id, {createUserId: msg.createUserId, receiveUsers: new Set<number>(), pack: pack})//msgId,{userId,receiveUsers,pack}

    }

    public clear(msg: any) {
        let pack = JSON.stringify({action: "clear", msgId: msg.id});
        this.clientIds_.forEach((clientAccessUserSet, c) => {

            if (clientAccessUserSet.has(msg.createUserId)) {
                c.send(pack);
            }

        })
        this.effective_.delete(msg.id);

    }

    public clearById(id: string) {
        let msg = this.effective_.get(id);
        let pack: any = JSON.stringify({action: "clear", msgId: id});
        this.clientIds_.forEach((clientAccessUserSet, c) => {
            if (msg && clientAccessUserSet.has(msg.createUserId)) {
                c.send(pack);
            }
        })
        this.effective_.delete(id);

    }

    public addReceiveUser(msgId: string, receiveUserId: number) {
        let msg = this.effective_.get(msgId);
        if (msg) {
            msg.receiveUsers.add(receiveUserId);
        }

    }
}

function parseToken(token: string) {

    return parseInt(token);
}

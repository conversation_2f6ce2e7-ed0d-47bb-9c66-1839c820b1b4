export class MyLogger {
    public static log(msg: any) {
        MyLogger.info(msg);
    }

    public static info(msg: any) {
        console.log(`${MyLogger.dateStr()} -> ${msg}`);
    }

    public static error(msg: any) {
        console.error(`${MyLogger.dateStr()} -> ${msg}`);
    }

    private static dateStr(): string {
        let date = new Date();
        return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}`;
    }
}
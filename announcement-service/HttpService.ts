import express from "express";
import {NextFunction} from "express-serve-static-core";
import * as bodyParser from 'body-parser';
import http from "http";

export class HttpService {
    private port: number;
    private app_: express.Application;
    private server_ :http.Server;

    constructor(port: number) {

        this.port = port;
        this.app_ = express();
        this.app_.use(bodyParser.urlencoded({extended: true}));
        this.app_.use(bodyParser.json({type:'application/*',strict:false}));
        this.app_.use(bodyParser.json());
        this.app_.use(function (error: any, req: express.Request, res: express.Response, next: NextFunction) {
            if (typeof (error) === "number")
                res.sendStatus(error);
            else {
                console.log(error.toString());
                res.sendStatus(500);
            }
            next();
        });
        this.server_=http.createServer(this.app_);
    }

    public bindRouter(router: express.Router) {
        this.app_.use('/api', router);
    }

    public start() {
        let str = `webApi start at http://localhost:${this.port}`;
        this.app_.listen(this.port, function () {
            console.log(str);
        });
    }

    public get server(): http.Server {
        return this.server_;
    }
}

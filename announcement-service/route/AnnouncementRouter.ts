import * as express from "express";
import {DbConnect} from "../db/DbConnect";
import AnnouncementHandler from "../handler/AnnouncementHandler";
import {NextFunction} from "express";
import moment from "moment/moment";
import {UserUtil} from "../Util/UserUtil";

export class AnnouncementRouter {
    private router_: express.Router;


    constructor(private db: DbConnect, private handler_: AnnouncementHandler) {
        this.router_ = express.Router();
        this.router_.post('/announcement/add', this.addToRecord.bind(this));
        this.router_.post('/announcement/receive', this.receive.bind(this));
        this.router_.delete('/announcement/delete', this.delete.bind(this));
        this.router_.get('/announcement/query', this.query.bind(this));

    }

    public get router(): express.Router {
        return this.router_;
    }

    private async query(req: express.Request, res: express.Response, next: NextFunction) {
        try {
            debugger;
            let startTime = new Date(req.query["startTime"] as any);
            let endTime = new Date(req.query["endTime"] as any);
            // let startTime = req.query["startTime"] as any;
            // let endTime = req.query["endTime"] as any;
            let userId = UserUtil.getUserId(req);
            let access;
            try {
                access = await UserUtil.getUserAccess('webgis_d_announcement', userId);
            } catch (e) {
                console.error('获取用户权限错误', userId, e)
            }
            let accessUsers = access && access.access_user_ids || [];


            let offset = Number(req.query["offset"]) || 0;
            let limit = Number(req.query["limit"]) || 10000;
            let search = req.query["search"] || null;
            const where: string[] = [];
            const params: any[] = [];

            if (!Number.isNaN(startTime.getTime())) {
                where.push(" time >= ? ");
                params.push(startTime);
            }
            if (!Number.isNaN(endTime.getTime())) {
                where.push(" time < ? ");
                params.push(endTime);
            }
            // if (startTime) {
            //     where.push(" time >= ? ");
            //     params.push(startTime);
            // }
            // if (endTime) {
            //     where.push(" time < ? ");
            //     params.push(endTime);
            // }

            if (search) {
                where.push(' (title regexp ? or content regexp ?  ) ');
                params.push(search);
                params.push(search);
            }

            //  where.push(` (createUserId in (${accessUsers.join(',')}) or createUserId is null) `);
            where.push(` (createUserId in (?) or createUserId is null) `);
            params.push(accessUsers)


            where.push(' deleted = 0 ')


            let pool = this.db.pool;
            let sql = 'select count(id) as total from zs_announcement ';
            if (where.length)
                sql += " where " + where.join(' and ');
            let total = 0;
            if (offset == 0) {
                total = await pool.loadScalar(sql, params);
            }
            sql = 'select * from zs_announcement ';
            if (where.length)
                sql += ' where ' + where.join('and');
            sql += ' order by time desc limit ?,? ';
            params.push(offset, limit);
            const data = await pool.loadRows(sql, params);
            // data.forEach((i) => {
            //     i.receiveUsers = undefined;
            // })
            res.json({total, data})
        } catch (e) {
            console.log('查询公告记录错误' + e);
            next(500);
        }
    }


    public async addToRecord(req: express.Request, res: express.Response, next: NextFunction) {
        try {
            let data: any = req.body;
            let announcement = this.handler_.formatMsg(data);
            if (announcement) {
                this.handler_.addRecord(announcement);
                res.status(200).json('success');
            } else {
                res.status(400).json('参数有误');
            }

        } catch (e) {
            console.log('添加公告错误' + e);
            next(500);
        }
    }

    private async receive(req: express.Request, res: express.Response, next: NextFunction) {
        try {
            let data: any = req.body;
            let id = data.id;
            let userId = data.userId;
            if (id && userId) {
                this.handler_.addReceiveUser(id, userId);
                res.status(200).json('success');
            } else {
                res.status(400).json('参数有误');
            }

        } catch (e) {
            console.log('添加公告错误' + e);
            next(500);
        }
    }

    private async delete(req: express.Request, res: express.Response, next: NextFunction) {
        try {
            let id: any = req.query["id"];
            let result = await this.handler_.delete(id);
            res.status(200).json('success');
        } catch (e) {
            console.log('删除重点人员记录错误' + e);
            next(500);
        }
    }

}
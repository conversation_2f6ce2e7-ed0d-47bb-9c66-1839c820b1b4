export const NTLMFLAG_NEGOTIATE_UNICODE: number;
export const NTLMFLAG_NEGOTIATE_OEM: number;
export const NTLMFLAG_REQUEST_TARGET: number;
export const NTLMFLAG_NEGOTIATE_SIGN: number;
export const NTLMFLAG_NEGOTIATE_SEAL: number;
export const NTLMFLAG_NEGOTIATE_DATAGRAM_STYLE: number;
export const NTLMFLAG_NEGOTIATE_LM_KEY: number;
export const NTLMFLAG_NEGOTIATE_NETWARE: number;
export const NTLMFLAG_NEGOTIATE_NTLM_KEY: number;
export const NTLMFLAG_NEGOTIATE_ANONYMOUS: number;
export const NTLMFLAG_NEGOTIATE_DOMAIN_SUPPLIED: number;
export const NTLMFLAG_NEGOTIATE_WORKSTATION_SUPPLIED: number;
export const NTLMFLAG_NEGOTIATE_LOCAL_CALL: number;
export const NTLMFLAG_NEGOTIATE_ALWAYS_SIGN: number;
export const NTLMFLAG_TARGET_TYPE_DOMAIN: number;
export const NTLMFLAG_TARGET_TYPE_SERVER: number;
export const NTLMFLAG_TARGET_TYPE_SHARE: number;
export const NTLMFLAG_NEGOTIATE_NTLM2_KEY: number;
export const NTLMFLAG_REQUEST_INIT_RESPONSE: number;
export const NTLMFLAG_REQUEST_ACCEPT_RESPONSE: number;
export const NTLMFLAG_REQUEST_NONNT_SESSION_KEY: number;
export const NTLMFLAG_NEGOTIATE_TARGET_INFO: number;
export const NTLMFLAG_NEGOTIATE_128: number;
export const NTLMFLAG_NEGOTIATE_KEY_EXCHANGE: number;
export const NTLMFLAG_NEGOTIATE_56: number;
//# sourceMappingURL=flags.d.ts.map
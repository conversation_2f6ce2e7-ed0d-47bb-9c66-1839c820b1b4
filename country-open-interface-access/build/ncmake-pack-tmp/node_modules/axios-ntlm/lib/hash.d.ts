export function createLMHash(password: any): any;
export function createNTLMHash(password: any): any;
export function createLMResponse(challenge: any, lmhash: any): any;
export function createNTLMResponse(challenge: any, ntlmhash: any): any;
export function createLMv2Response(type2message: any, username: any, ntlmhash: any, nonce: any, targetName: any): any;
export function createNTLMv2Response(type2message: any, username: any, ntlmhash: any, nonce: any, targetName: any): any;
export function createPseudoRandomValue(length: any): string;
//# sourceMappingURL=hash.d.ts.map
/**
 * 调用国家开放平台--交通部_身份认证接口调用说明
 */
import {MyLogger} from "../../util/MyLogger";
import axios from "axios";
import {ConfigUtil, RequestParams} from "../../util/ConfigUtil";
import AppSecretCache from "../AppSecretCache";
import {FileAccessor} from "../../util/FileAccessor";

export default class AuthenticationToken {
    private ready_: Promise<boolean>;
    private readyResolve_!: (val: boolean) => any;
    private identity_: Identity;
    private fileAccessor_: FileAccessor;
    private reloadTimer_?: NodeJS.Timer;

    constructor(private shenfenrenzhengSecret_: AppSecretCache) {
        this.fileAccessor_ = new FileAccessor(this.shenfenrenzhengSecret_.requestParams.name);
        this.ready_ = new Promise<boolean>((resolve, reject) => {
            this.readyResolve_ = resolve;
        });
        this.identity_ = {
            access_token: '',
            token_type: '',
            expires_in: 0
        };
        void this.init();
        this.shenfenrenzhengSecret_.on('refresh', () => {
            void this.reloadNow();
        });
    }

    public get ready(): Promise<boolean> {
        return this.ready_;
    }

    public get token(): string {
        return this.identity_.access_token;
    }

    private async init() {
        let authentication = this.fileAccessor_.load();
        MyLogger.log('AuthenticationToken init', authentication && JSON.stringify(authentication))
        if (authentication && Number(authentication.expires_in) > new Date().getTime()) {
            this.identity_ = this.fileAccessor_.load();
            this.readyResolve_(true);
        } else {
            this.fileAccessor_.remove();
            await this.getData();
        }
        //此时已经拿到token,开始计算下次的刷新
        if (this.identity_.expires_in) {
            this.reloadTimer_ = setTimeout(() => {
                this.init();
            }, this.identity_.expires_in - new Date().getTime());
        }
    }

    public async reloadNow() {
        MyLogger.log('AuthenticationToken reloadNow')
        if(this.reloadTimer_)
            clearTimeout(this.reloadTimer_);
        this.fileAccessor_.remove();
        await this.getData();

    }

    private async getData() {
        let data = await this.loadAccessToken_();
        if (data) {
            this.identity_ = data;
            this.readyResolve_(true);
            //把当前接口返回存入文件中，方便下次调用
            this.fileAccessor_.save(data);
        } else {
            //TODO：接口返回的非正常结果？？？
        }
    }

    private async loadAccessToken_() {
        try {
            let rtime = new Date().getTime();
            console.log({
                "Content-Type": "application/x-www-form-urlencoded",
                gjzwfwpt_rid: this.shenfenrenzhengSecret_.requestParams.rid,//请求者标识
                gjzwfwpt_sid: this.shenfenrenzhengSecret_.requestParams.sid,//'要调用的服务编码'
                gjzwfwpt_rtime: this.shenfenrenzhengSecret_.signTime,
                gjzwfwpt_sign: this.shenfenrenzhengSecret_.sign//'发送方签名（参考附件中的签名密钥获取接口）。'
            })
            //TODO: 更改成完整接口
            let {data} = await axios.post(`${ConfigUtil.interfaceUrl}/gateway/httpproxy?client_id=${ConfigUtil.jiaoTongAuthentication.clientId}&client_secret=${ConfigUtil.jiaoTongAuthentication.clientSecret}&grant_type=${ConfigUtil.jiaoTongAuthentication.grantType}&scope=${ConfigUtil.jiaoTongAuthentication.scope}`, {}, {
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded",
                    gjzwfwpt_rid: this.shenfenrenzhengSecret_.requestParams.rid,//请求者标识
                    gjzwfwpt_sid: this.shenfenrenzhengSecret_.requestParams.sid,//'要调用的服务编码'
                    gjzwfwpt_rtime: this.shenfenrenzhengSecret_.signTime,
                    gjzwfwpt_sign: this.shenfenrenzhengSecret_.sign//'发送方签名（参考附件中的签名密钥获取接口）。'
                }
            });
            MyLogger.log('交通部身份认证access_token接口返回结果', JSON.stringify(data));
            if (data && data.access_token) {
                //此处把过期时间接口返回的秒数改为过期时间的时间戳
                data.expires_in = new Date().getTime() + (Number(data.expires_in) * 1000);
                return data;
            }
            MyLogger.log('更新交通部身份认证access_token接口时间戳后--', JSON.stringify(data));
            return undefined;
        } catch (e) {
            MyLogger.log('交通部身份认证access_token接口获取错误', e);
            return undefined;
        }
    }
}

interface Identity {
    access_token: string;//身份令牌
    token_type: string;//令牌类型
    expires_in: number;//过期时间
}

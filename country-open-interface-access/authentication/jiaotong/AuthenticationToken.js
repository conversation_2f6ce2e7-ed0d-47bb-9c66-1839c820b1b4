"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 调用国家开放平台--交通部_身份认证接口调用说明
 */
const MyLogger_1 = require("../../util/MyLogger");
const axios_1 = __importDefault(require("axios"));
const ConfigUtil_1 = require("../../util/ConfigUtil");
const FileAccessor_1 = require("../../util/FileAccessor");
class AuthenticationToken {
    shenfenrenzhengSecret_;
    ready_;
    readyResolve_;
    identity_;
    fileAccessor_;
    reloadTimer_;
    constructor(shenfenrenzhengSecret_) {
        this.shenfenrenzhengSecret_ = shenfenrenzhengSecret_;
        this.fileAccessor_ = new FileAccessor_1.FileAccessor(this.shenfenrenzhengSecret_.requestParams.name);
        this.ready_ = new Promise((resolve, reject) => {
            this.readyResolve_ = resolve;
        });
        this.identity_ = {
            access_token: '',
            token_type: '',
            expires_in: 0
        };
        void this.init();
        this.shenfenrenzhengSecret_.on('refresh', () => {
            void this.reloadNow();
        });
    }
    get ready() {
        return this.ready_;
    }
    get token() {
        return this.identity_.access_token;
    }
    async init() {
        let authentication = this.fileAccessor_.load();
        MyLogger_1.MyLogger.log('AuthenticationToken init', authentication && JSON.stringify(authentication));
        if (authentication && Number(authentication.expires_in) > new Date().getTime()) {
            this.identity_ = this.fileAccessor_.load();
            this.readyResolve_(true);
        }
        else {
            this.fileAccessor_.remove();
            await this.getData();
        }
        //此时已经拿到token,开始计算下次的刷新
        if (this.identity_.expires_in) {
            this.reloadTimer_ = setTimeout(() => {
                this.init();
            }, this.identity_.expires_in - new Date().getTime());
        }
    }
    async reloadNow() {
        MyLogger_1.MyLogger.log('AuthenticationToken reloadNow');
        if (this.reloadTimer_)
            clearTimeout(this.reloadTimer_);
        this.fileAccessor_.remove();
        await this.getData();
    }
    async getData() {
        let data = await this.loadAccessToken_();
        if (data) {
            this.identity_ = data;
            this.readyResolve_(true);
            //把当前接口返回存入文件中，方便下次调用
            this.fileAccessor_.save(data);
        }
        else {
            //TODO：接口返回的非正常结果？？？
        }
    }
    async loadAccessToken_() {
        try {
            let rtime = new Date().getTime();
            console.log({
                "Content-Type": "application/x-www-form-urlencoded",
                gjzwfwpt_rid: this.shenfenrenzhengSecret_.requestParams.rid, //请求者标识
                gjzwfwpt_sid: this.shenfenrenzhengSecret_.requestParams.sid, //'要调用的服务编码'
                gjzwfwpt_rtime: this.shenfenrenzhengSecret_.signTime,
                gjzwfwpt_sign: this.shenfenrenzhengSecret_.sign //'发送方签名（参考附件中的签名密钥获取接口）。'
            });
            //TODO: 更改成完整接口
            let { data } = await axios_1.default.post(`${ConfigUtil_1.ConfigUtil.interfaceUrl}/gateway/httpproxy?client_id=${ConfigUtil_1.ConfigUtil.jiaoTongAuthentication.clientId}&client_secret=${ConfigUtil_1.ConfigUtil.jiaoTongAuthentication.clientSecret}&grant_type=${ConfigUtil_1.ConfigUtil.jiaoTongAuthentication.grantType}&scope=${ConfigUtil_1.ConfigUtil.jiaoTongAuthentication.scope}`, {}, {
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded",
                    gjzwfwpt_rid: this.shenfenrenzhengSecret_.requestParams.rid, //请求者标识
                    gjzwfwpt_sid: this.shenfenrenzhengSecret_.requestParams.sid, //'要调用的服务编码'
                    gjzwfwpt_rtime: this.shenfenrenzhengSecret_.signTime,
                    gjzwfwpt_sign: this.shenfenrenzhengSecret_.sign //'发送方签名（参考附件中的签名密钥获取接口）。'
                }
            });
            MyLogger_1.MyLogger.log('交通部身份认证access_token接口返回结果', JSON.stringify(data));
            if (data && data.access_token) {
                //此处把过期时间接口返回的秒数改为过期时间的时间戳
                data.expires_in = new Date().getTime() + (Number(data.expires_in) * 1000);
                return data;
            }
            MyLogger_1.MyLogger.log('更新交通部身份认证access_token接口时间戳后--', JSON.stringify(data));
            return undefined;
        }
        catch (e) {
            MyLogger_1.MyLogger.log('交通部身份认证access_token接口获取错误', e);
            return undefined;
        }
    }
}
exports.default = AuthenticationToken;

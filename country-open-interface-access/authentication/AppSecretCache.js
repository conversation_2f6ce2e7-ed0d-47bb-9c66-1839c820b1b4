"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 国家开放平台签名秘钥缓存实体通用类
 * @params
 */
const Encrypt_1 = require("../util/Encrypt");
const axios_1 = __importDefault(require("axios"));
const ConfigUtil_1 = require("../util/ConfigUtil");
const MyLogger_1 = require("../util/MyLogger");
const FileAccessor_1 = require("../util/FileAccessor");
const EventSource_1 = __importDefault(require("../util/EventSource"));
const schedule = require('node-schedule');
class AppSecretCache extends EventSource_1.default {
    requestParams_;
    secret_;
    sign_;
    signTime_;
    signTimer_;
    ready_;
    readyResolve_;
    fileAccessor_;
    constructor(requestParams_) {
        super();
        this.requestParams_ = requestParams_;
        this.fileAccessor_ = new FileAccessor_1.FileAccessor(this.requestParams_.name);
        this.ready_ = new Promise((resolve, reject) => {
            this.readyResolve_ = resolve;
        });
        void this.init_();
        //TODO:每天0点1分10秒起，2小时刷新
        schedule.scheduleJob('10 1 0/2 * * *', () => {
            if (this.signTimer_)
                clearTimeout(this.signTimer_);
            void this.init_();
        });
    }
    get ready() {
        return this.ready_;
    }
    get requestParams() {
        return this.requestParams_;
    }
    get secret() {
        return this.secret_;
    }
    get sign() {
        return this.sign_;
    }
    get signTime() {
        return this.signTime_;
    }
    async init_() {
        try {
            let secretCache = this.fileAccessor_.load();
            if (secretCache && Number(secretCache.secretEndTime) > new Date().getTime()) {
                this.secret_ = (0, Encrypt_1.decrypt)(secretCache.secret, this.requestParams_.appKey);
                // this.secret_ = await decodeAes(secretCache.secret, this.requestParams_.appKey);
            }
            else {
                this.fileAccessor_.remove();
                await this.getData();
            }
            //开始刷新签名
            this.refreshSign();
            this.readyResolve_(true);
            this.emit('refresh');
        }
        catch (e) {
            MyLogger_1.MyLogger.log(`初始化获取${this.requestParams_.name} api签名秘钥失败`, e);
        }
    }
    refreshSign() {
        let time = new Date().getTime();
        this.signTime_ = time + '';
        this.sign_ = this.loadSign(this.requestParams_.sid, this.requestParams_.rid, time, this.secret_);
        this.signTimer_ = setTimeout(() => {
            this.refreshSign();
        }, 1000 * 60 * 10);
    }
    async getData() {
        let time = new Date().getTime();
        let sign = this.loadSign(this.requestParams_.sid, this.requestParams_.rid, time, this.requestParams_.appKey);
        let secret = await this.getAppSecret(sign, time);
        if (secret) {
            this.secret_ = (0, Encrypt_1.decrypt)(secret.secret, this.requestParams_.appKey);
            // this.secret_ = await decodeAes(secret.secret, this.requestParams_.appKey);
            MyLogger_1.MyLogger.log(`初始化获取${this.requestParams_.name} api签名秘钥成功`);
            this.fileAccessor_.save(secret);
        }
    }
    loadSign(sid, rid, time, appKey) {
        return (0, Encrypt_1.encryptHmacSha256)(sid, rid, time, appKey);
    }
    async getAppSecret(sign, time) {
        let { data } = await axios_1.default.post(ConfigUtil_1.ConfigUtil.interfaceUrl + '/authz/authSystem/getAppSecret', {
            gjzwfwpt_rid: this.requestParams_.rid,
            gjzwfwpt_sid: this.requestParams_.sid,
            gjzwfwpt_rtime: time + '',
            gjzwfwpt_sign: sign
        });
        MyLogger_1.MyLogger.log('签名密钥获取接口调用结果', this.requestParams_.rid, JSON.stringify(data));
        if (data && data.code == 0) {
            return data.data;
        }
        return undefined;
    }
}
exports.default = AppSecretCache;

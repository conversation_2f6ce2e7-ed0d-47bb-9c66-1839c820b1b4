/**
 * 国家开放平台签名秘钥缓存实体通用类
 * @params
 */
import {decodeAes, decrypt, encryptHmacSha256} from "../util/Encrypt";
import axios from "axios";
import {ConfigUtil, RequestParams} from "../util/ConfigUtil";
import {MyLogger} from "../util/MyLogger";
import {FileAccessor} from "../util/FileAccessor";
import EventSource from "../util/EventSource";

const schedule = require('node-schedule');

export default class AppSecretCache extends EventSource {
    private secret_!: string;
    private sign_!: string;
    private signTime_!: string;
    private signTimer_!: NodeJS.Timer;
    private ready_!: Promise<boolean>;
    private readyResolve_!: (val: boolean) => void;
    private fileAccessor_: FileAccessor;

    constructor(private requestParams_: RequestParams) {
        super();
        this.fileAccessor_ = new FileAccessor(this.requestParams_.name);
        this.ready_ = new Promise((resolve, reject) => {
            this.readyResolve_ = resolve;
        });
        void this.init_();
        //TODO:每天0点1分10秒起，2小时刷新
        schedule.scheduleJob('10 1 0/2 * * *', () => {
            if (this.signTimer_) clearTimeout(this.signTimer_);
            void this.init_();
        });
    }

    public get ready(): Promise<boolean> {
        return this.ready_;
    }

    public get requestParams(): RequestParams {
        return this.requestParams_;
    }

    public get secret(): string {
        return this.secret_;
    }

    public get sign(): string {
        return this.sign_;
    }

    public get signTime(): string {
        return this.signTime_;
    }

    async init_() {
        try {
            let secretCache = this.fileAccessor_.load();
            if (secretCache && Number(secretCache.secretEndTime) > new Date().getTime()) {
                this.secret_ = decrypt(secretCache.secret, this.requestParams_.appKey);
                // this.secret_ = await decodeAes(secretCache.secret, this.requestParams_.appKey);
            } else {
                this.fileAccessor_.remove();
                await this.getData();
            }
            //开始刷新签名
            this.refreshSign();
            this.readyResolve_(true);
            this.emit('refresh')
        } catch (e) {
            MyLogger.log(`初始化获取${this.requestParams_.name} api签名秘钥失败`, e);
        }
    }

    private refreshSign() {
        let time = new Date().getTime();
        this.signTime_ = time + '';
        this.sign_ = this.loadSign(this.requestParams_.sid, this.requestParams_.rid, time, this.secret_);
        this.signTimer_ = setTimeout(() => {
            this.refreshSign();
        }, 1000 * 60 * 10);
    }

    private async getData() {
        let time = new Date().getTime();
        let sign = this.loadSign(this.requestParams_.sid, this.requestParams_.rid, time, this.requestParams_.appKey);
        let secret = await this.getAppSecret(sign, time);
        if (secret) {
            this.secret_ = decrypt(secret.secret, this.requestParams_.appKey);
            // this.secret_ = await decodeAes(secret.secret, this.requestParams_.appKey);
            MyLogger.log(`初始化获取${this.requestParams_.name} api签名秘钥成功`);
            this.fileAccessor_.save(secret);
        }
    }

    private loadSign(sid: string, rid: string, time: number, appKey: string) {
        return encryptHmacSha256(sid, rid, time, appKey);
    }

    private async getAppSecret(sign: string, time: number): Promise<Secret | undefined> {
        let {data} = await axios.post(ConfigUtil.interfaceUrl + '/authz/authSystem/getAppSecret', {
            gjzwfwpt_rid: this.requestParams_.rid,
            gjzwfwpt_sid: this.requestParams_.sid,
            gjzwfwpt_rtime: time + '',
            gjzwfwpt_sign: sign
        });
        MyLogger.log('签名密钥获取接口调用结果', this.requestParams_.rid,JSON.stringify(data));
        if (data && data.code == 0) {
            return data.data as Secret;
        }
        return undefined;
    }

}

interface Secret {
    secret: string;
    secretEndTime: number;
}

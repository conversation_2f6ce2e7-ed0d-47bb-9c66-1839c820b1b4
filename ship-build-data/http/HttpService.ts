import express from "express";
import bodyParser from "body-parser";
import * as http from 'http';
import {NextFunction} from "express-serve-static-core";
import {ConfigUtil} from "../util/ConfigUtil";

export class HttpService {
    private app_: express.Application;
    private server_: http.Server;

    constructor() {
        this.app_ = express();
        this.app_.use(bodyParser.json());
        this.app_.use(bodyParser.urlencoded({extended: false}));
        this.app_.use(function (error: any, req: express.Request, res: express.Response, next: NextFunction) {
            if (typeof (error) === "number")
                res.sendStatus(error);
            else {
                console.log(error.toString());
                res.sendStatus(500);
            }
            next();
        });
        this.server_ = http.createServer(this.app_);
    }

    public get server(): http.Server {
        return this.server_;
    }

    public bindRouter(router: express.Router) {
        this.app_.use('/api', router);
    }

    public start() {
        let str = `webApi start at http://localhost:${ConfigUtil.port}`;
        console.log('Starting server...');
        this.server_.listen(ConfigUtil.port, function () {
            console.log(str);
        });
        console.log('Server listen command executed');
    }
}

{"name": "@types/send", "version": "0.17.5", "description": "TypeScript definitions for send", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/send", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>red"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON><PERSON>bas"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/send"}, "scripts": {}, "dependencies": {"@types/mime": "^1", "@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "8088c014f9e7c4c38cfce1ccf4f348a612ebe7009a733d262a12236edd4e90d7", "typeScriptVersion": "5.1"}
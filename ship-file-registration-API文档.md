# ship-file-registration API 接口文档

## 📋 服务信息

- **服务名称**: ship-file-registration (船舶档案登记服务)
- **端口**: 12323
- **状态**: ✅ 正在运行
- **基础URL**: `http://localhost:12323`

## 🚢 船舶档案管理 API

### 1. 获取船舶列表
```http
GET /api/shipRegistration/ship
```

**查询参数**:
```javascript
{
  "pageIndex": 0,           // 页码 (必需)
  "pageSize": 10,           // 每页数量 (必需)
  "shipName": "船舶名称",    // 船舶名称 (模糊查询)
  "mmsi": "*********",      // MMSI号码
  "shipType": ["渔船"],     // 船舶类型数组
  "home_port": "港口名",    // 船籍港
  "isPriorityShip": 1,      // 是否重点船舶 (0/1)
  "declaration_type": 1,    // 申报类型 (1:正常, 2:注销)
  "ship_company": "公司名", // 船舶公司
  "gxpcs": "管辖派出所",    // 管辖派出所
  "operating_type": "经营类型", // 经营类型
  "region": "区域代码",     // 区域
  "org_ids": "[1,2,3]"      // 组织ID数组 (JSON字符串)
}
```

**响应示例**:
```json
{
  "total": 100,
  "items": [
    {
      "id": 1,
      "ship_name": "浙台渔12345",
      "mmsi": "*********",
      "ship_type": "渔船",
      "home_port": "台州港",
      "ship_owner": "张三",
      "registrant_time": "2023-01-01 10:00:00"
    }
  ]
}
```

### 2. 新增船舶档案
```http
POST /api/shipRegistration/ship
```

**请求体**:
```json
{
  "ship_name": "浙台渔12345",        // 船舶名称 (必需)
  "ship_en_name": "Ship Name",      // 船舶英文名
  "mmsi": "*********",              // MMSI号码
  "ship_type": "渔船",              // 船舶类型
  "home_port": "台州港",            // 船籍港
  "ship_owner": "张三",             // 船舶所有人
  "ship_power": "100KW",            // 船舶功率
  "ship_length": 15.5,              // 船长 (米)
  "ship_width": 4.2,                // 船宽 (米)
  "ship_height": 2.8,               // 船高 (米)
  "ship_material": "钢质",          // 船体材料
  "ship_status": "正常",            // 船舶状态
  "ton": 50.5,                      // 吨位
  "host_number_approved": 8,        // 核定载客人数
  "ship_company": "某某渔业公司",   // 船舶公司
  "ship_use": "捕捞",               // 船舶用途
  "operating_type": "近海捕捞",     // 经营类型
  "construction_manufacturer": "造船厂", // 建造厂商
  "permanent_berth": "1号泊位",     // 常泊泊位
  "identification_code": "ABC123",   // 识别代码
  "org_id": 21,                     // 组织ID
  "org_name": "台州海事局",         // 组织名称
  "org_code": "331000000000",       // 组织代码
  "region": 1,                      // 区域代码
  "remarks": "备注信息"             // 备注
}
```

### 3. 更新船舶档案
```http
PUT /api/shipRegistration/ship
```

**请求体**: 同新增，需包含 `id` 字段

### 4. 删除船舶档案
```http
DELETE /api/shipRegistration/ship?ships=[1,2,3]
```

### 5. 根据MMSI获取船舶信息
```http
GET /api/shipRegistration/getShipByMmsi?mmsi=*********
```

### 6. 船舶文件上传
```http
POST /api/shipRegistration/shipFileUpload
Content-Type: multipart/form-data
```

**表单数据**:
- `file`: Excel文件
- `org_id`: 组织ID
- `org_name`: 组织名称
- `region`: 区域代码
- `org_code`: 组织代码

## 👥 船员档案管理 API

### 1. 获取船员列表
```http
GET /api/shipRegistration/shipMember
```

**查询参数**:
```javascript
{
  "pageIndex": 0,              // 页码 (必需)
  "pageSize": 10,              // 每页数量 (必需)
  "name": "张三",              // 船员姓名 (模糊查询)
  "phone": "13800138000",      // 手机号码 (模糊查询)
  "relationship": "船长",      // 船上职务
  "ship_operation_code": "ABC", // 船舶操作码 (模糊查询)
  "bound": 1,                  // 是否绑定船舶 (0/1)
  "boundShipName": "船舶名",   // 绑定船舶名称 (模糊查询)
  "id_number": "身份证号",     // 身份证号 (模糊查询)
  "focus": "是",               // 是否重点关注
  "isDel": 0,                  // 是否删除 (0:未删除, 1:已删除)
  "declaration_type": 1,       // 申报类型
  "org_ids": "[1,2,3]"         // 组织ID数组 (JSON字符串)
}
```

### 2. 新增船员档案
```http
POST /api/shipRegistration/shipMember
```

**请求体**:
```json
{
  "name": "张三",                    // 姓名 (必需)
  "id_number": "330102199001011234", // 身份证号 (必需)
  "gender": "男",                    // 性别
  "phone": "13800138000",            // 手机号码
  "relationship": "船长",            // 船上职务
  "nationality": "中国",             // 国籍
  "id_type": "身份证",               // 证件类型
  "affiliated_unit": "某某渔业公司", // 所属单位
  "census_address": "户籍地址",      // 户籍地址
  "current_address": "现住址",       // 现住址
  "nation": "汉族",                  // 民族
  "birthday": "1990-01-01",          // 出生日期
  "emergency_contact_people": "李四", // 紧急联系人
  "emergency_contact_phone": "13900139000", // 紧急联系人电话
  "logout": "否",                    // 是否注销
  "focus": "否",                     // 是否重点关注
  "bound": 1,                        // 是否绑定船舶
  "bound_ship_name": "浙台渔12345",  // 绑定船舶名称
  "ship_operation_code": "ABC123",   // 船舶操作码
  "org_id": 21,                      // 组织ID
  "org_name": "台州海事局",          // 组织名称
  "org_code": "331000000000",        // 组织代码
  "region": 1,                       // 区域代码
  "remarks": "备注信息"              // 备注
}
```

### 3. 船员历史记录
```http
GET /api/shipRegistration/shipMemberhistory
```

**查询参数**:
```javascript
{
  "pageIndex": 0,        // 页码 (必需)
  "pageSize": 10,        // 每页数量 (必需)
  "ship_name": "船舶名", // 船舶名称
  "crew_name": "船员名", // 船员姓名
  "ship_id": 1,          // 船舶ID
  "crew_id": 1,          // 船员ID
  "is_now": 1            // 是否当前在船 (0/1)
}
```

### 4. 船员上船操作
```http
PUT /api/shipRegistration/addShipMember
```

**请求体**:
```json
{
  "id": [1, 2, 3],        // 船员ID数组
  "time": "2023-01-01 10:00:00", // 上船时间 (可选)
  "shipData": {           // 船舶信息
    "id": 1,
    "ship_name": "浙台渔12345",
    "mmsi": "*********",
    "ship_operation_code": "ABC123",
    "org_id": 21,
    "org_name": "台州海事局",
    "org_code": "331000000000",
    "region": 1
  }
}
```

### 5. 船员离船操作
```http
PUT /api/shipRegistration/delShipMember
```

**请求体**:
```json
{
  "data": {
    "id_number": "330102199001011234", // 身份证号
    "bound_ship_name": "浙台渔12345",  // 船舶名称
    "resignation_time": "2023-01-01 18:00:00" // 离船时间 (可选)
  }
}
```

## 🎯 重点船舶管理 API

### 1. 获取重点船舶列表
```http
GET /api/shipRegistration/priorityShip
```

**查询参数**:
```javascript
{
  "pageIndex": 0,           // 页码 (必需)
  "pageSize": 10,           // 每页数量 (必需)
  "shipName": "船舶名称",   // 船舶名称 (模糊查询)
  "mmsi": *********,        // MMSI号码
  "cause": "原因",          // 列管原因 (模糊查询)
  "remark": "备注",         // 备注 (模糊查询)
  "keynoteTypes": ["类型1"], // 重点类型数组
  "org_ids": "[1,2,3]"      // 组织ID数组 (JSON字符串)
}
```

### 2. 新增重点船舶
```http
POST /api/shipRegistration/priorityShip
```

**请求体**:
```json
{
  "name": "浙台渔12345",           // 船舶名称 (必需)
  "mmsi": "*********",            // MMSI号码 (必需)
  "homePort": "台州港",           // 船籍港
  "administrator": "张三",        // 管理员
  "phone": "13800138000",         // 联系电话
  "address": "详细地址",          // 地址
  "controlTime": "2023-01-01",    // 管控时间
  "cause": "违规捕捞",            // 列管原因
  "currentSituation": "整改中",   // 当前情况
  "remark": "备注信息",           // 备注
  "copInCharge": "李四",          // 负责民警
  "keynoteType": "安全隐患",      // 重点类型
  "shipNo": "浙台渔12345",        // 船舶编号
  "ship_company": "某某渔业公司", // 船舶公司
  "ship_type": "渔船",            // 船舶类型
  "org_id": 21,                   // 组织ID
  "org_name": "台州海事局",       // 组织名称
  "org_code": "331000000000",     // 组织代码
  "region": 1                     // 区域代码
}
```

## 📊 其他功能 API

### 1. 获取船舶MMSI列表
```http
GET /api/shipRegistration/shipMmsiList
```

### 2. 进出港船员信息
```http
GET /api/shipRegistration/inoutcrew?ship_name=船舶名称
```

### 3. 船员进出港历史
```http
GET /api/shipRegistration/inoutcrewHistory?id_card_num=身份证号&name=姓名
```

### 4. 数据同步接口 (外部调用)
```http
POST /api/shipRegistration/syncYuChuanJiBen
```

## 🔧 船舶类型代码对照

| 船舶类型 | 类型代码 |
|---------|----------|
| 公务船 | 905 |
| 游船 | 105 |
| 渡船 | 104 |
| 渔船 | 9001 |
| 高速船 | 106 |
| 危化品运输船 | 302 |
| 普通运输船 | 200 |
| 工程作业船 | 400 |
| 其他 | 0 |

## 📝 响应格式

### 成功响应
```json
{
  "code": 200,
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "code": 500,
  "message": "错误信息"
}
```

## � 文件上传 API

### 1. 船舶Excel批量导入
```http
POST /api/shipRegistration/shipFileUpload
Content-Type: multipart/form-data
```

**Excel格式要求**:
| 列序号 | 字段名 | 说明 | 示例 |
|-------|--------|------|------|
| A | ship_name | 船舶名称 | 浙台渔12345 |
| B | ship_en_name | 船舶英文名 | Ship Name |
| C | mmsi | MMSI号码 | ********* |
| D | ship_type | 船舶类型 | 渔船 |
| E | declaration_type | 申报类型 | 正常/注销 |
| F | home_port | 船籍港 | 台州港 |
| G | ship_status | 船舶状态 | 正常 |
| H | host_number_approved | 核定载客人数 | 8 |
| I | ship_length | 船长(米) | 15.5 |
| J | ship_width | 船宽(米) | 4.2 |
| K | ship_height | 船高(米) | 2.8 |
| L | ton | 吨位 | 50.5 |
| M | ship_power | 船舶功率 | 100KW |
| N | ship_material | 船体材料 | 钢质 |
| O | ship_person_in_charge_name | 负责人姓名 | 张三 |
| P | ship_person_in_charge_phone | 负责人电话 | 13800138000 |
| Q | ship_company | 船舶公司 | 某某渔业公司 |
| R | ship_use | 船舶用途 | 捕捞 |
| S | ship_character | 船舶性质 | 营运 |
| T | ship_source | 船舶来源 | 新建 |
| U | operating_type | 经营类型 | 近海捕捞 |
| V | construction_manufacturer | 建造厂商 | 造船厂 |
| W | permanent_berth | 常泊泊位 | 1号泊位 |
| X | identification_code | 识别代码 | ABC123 |
| Y | remarks | 备注 | 备注信息 |
| Z | 照片 | 船舶照片 | (图片) |

### 2. 船员Excel批量导入
```http
POST /api/shipRegistration/shipMemberFileUpload
Content-Type: multipart/form-data
```

**Excel格式要求**:
| 列序号 | 字段名 | 说明 | 示例 |
|-------|--------|------|------|
| A | relationship | 船上职务 | 船长 |
| B | nationality | 国籍 | 中国 |
| C | id_type | 证件类型 | 身份证 |
| D | id_number | 身份证号 | 330102199001011234 |
| E | name | 姓名 | 张三 |
| F | gender | 性别 | 男 |
| G | phone | 手机号码 | 13800138000 |
| H | declaration_type | 申报类型 | 上船/离船 |
| I | focus | 重点关注 | 是/否 |
| J | census_address | 户籍地址 | 浙江省台州市 |
| K | current_address | 现住址 | 浙江省台州市 |
| L | remarks | 备注 | 备注信息 |

## �🔍 测试示例

### 获取船舶列表
```bash
curl "http://localhost:12323/api/shipRegistration/ship?pageIndex=0&pageSize=10"
```

### 新增船舶
```bash
curl -X POST http://localhost:12323/api/shipRegistration/ship \
  -H "Content-Type: application/json" \
  -d '{
    "ship_name": "测试船舶",
    "mmsi": "*********",
    "ship_type": "渔船",
    "home_port": "台州港"
  }'
```

### 获取船员列表
```bash
curl "http://localhost:12323/api/shipRegistration/shipMember?pageIndex=0&pageSize=10"
```

### 获取重点船舶列表
```bash
curl "http://localhost:12323/api/shipRegistration/priorityShip?pageIndex=0&pageSize=10"
```

### 根据MMSI查询船舶
```bash
curl "http://localhost:12323/api/shipRegistration/getShipByMmsi?mmsi=*********"
```

## 🚨 注意事项

1. **分页参数**: 所有列表查询接口都需要 `pageIndex` 和 `pageSize` 参数
2. **数据格式**: 日期格式统一使用 `YYYY-MM-DD HH:mm:ss`
3. **文件上传**: 支持 Excel (.xlsx) 格式，需要按照指定的列顺序
4. **权限控制**: 当前版本无身份验证，生产环境需要添加权限控制
5. **数据库配置**: 确保数据库连接配置正确
6. **文件存储**: 上传的文件存储在配置的 `filesUrl` 目录中

## 📊 数据库表结构

### 主要表
- `ship_registration_info` - 船舶登记信息表
- `ship_member_registration_info` - 船员登记信息表
- `ship_crew_history` - 船员历史记录表
- `priorityship` - 重点船舶表
- `inout_info` - 进出港信息表
- `crew_information_in_and_out_of_port` - 进出港船员信息表
